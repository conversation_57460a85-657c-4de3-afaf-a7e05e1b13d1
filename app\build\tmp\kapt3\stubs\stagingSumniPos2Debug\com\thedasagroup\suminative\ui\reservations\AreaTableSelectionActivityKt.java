package com.thedasagroup.suminative.ui.reservations;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000D\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0003\u001aH\u0010\u0000\u001a\u00020\u00012\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0018\u0010\u0005\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u00062\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\n2\u0006\u0010\u000b\u001a\u00020\fH\u0007\u001a$\u0010\r\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u000f2\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u0011H\u0007\u001a\u001e\u0010\u0012\u001a\u00020\u00012\u0006\u0010\u0013\u001a\u00020\u00072\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001aB\u0010\u0015\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u000f2\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0012\u0010\u0016\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u00112\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a(\u0010\u0018\u001a\u00020\u00012\u0006\u0010\u0019\u001a\u00020\b2\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00010\n2\b\b\u0002\u0010\u001a\u001a\u00020\u001bH\u0007\u001a*\u0010\u001c\u001a\u00020\u00012\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\b0\u00032\u0012\u0010\u0016\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\u0011H\u0007\u00a8\u0006\u001e"}, d2 = {"AreaTableSelectionScreen", "", "excludedTableIds", "", "", "onAreaTableSelected", "Lkotlin/Function2;", "Lcom/thedasagroup/suminative/data/model/response/reservations/Area;", "Lcom/thedasagroup/suminative/data/model/response/reservations/Table;", "onBackPressed", "Lkotlin/Function0;", "viewModel", "Lcom/thedasagroup/suminative/ui/reservations/ReservationsViewModel;", "AreaSelectionContent", "state", "Lcom/thedasagroup/suminative/ui/reservations/ReservationsState;", "onAreaSelected", "Lkotlin/Function1;", "AreaCard", "area", "onClick", "TableSelectionContent", "onTableSelected", "onBackToAreas", "TableCard", "table", "modifier", "Landroidx/compose/ui/Modifier;", "TableBoxLayout", "tables", "app_stagingSumniPos2Debug"})
public final class AreaTableSelectionActivityKt {
    
    @androidx.compose.runtime.Composable()
    public static final void AreaTableSelectionScreen(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Integer> excludedTableIds, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super com.thedasagroup.suminative.data.model.response.reservations.Area, ? super com.thedasagroup.suminative.data.model.response.reservations.Table, kotlin.Unit> onAreaTableSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackPressed, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.reservations.ReservationsViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AreaSelectionContent(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.reservations.ReservationsState state, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.reservations.Area, kotlin.Unit> onAreaSelected) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AreaCard(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.reservations.Area area, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TableSelectionContent(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.reservations.ReservationsState state, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Integer> excludedTableIds, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.reservations.Table, kotlin.Unit> onTableSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackToAreas) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TableCard(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.reservations.Table table, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TableBoxLayout(@org.jetbrains.annotations.NotNull()
    java.util.List<com.thedasagroup.suminative.data.model.response.reservations.Table> tables, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.thedasagroup.suminative.data.model.response.reservations.Table, kotlin.Unit> onTableSelected) {
    }
}