package com.thedasagroup.suminative.scan;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\u0018\u0000 \u001b2\u00020\u0001:\u0001\u001bB\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\b\u0010\r\u001a\u00020\u000eH\u0002J\b\u0010\u000f\u001a\u00020\u000eH\u0002J\u0010\u0010\u0010\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u0012H\u0002J\u0012\u0010\u0013\u001a\u00020\u000e2\b\u0010\u0014\u001a\u0004\u0018\u00010\u0015H\u0014J\b\u0010\u0016\u001a\u00020\u000eH\u0015J\b\u0010\u0017\u001a\u00020\u000eH\u0014J\b\u0010\u0018\u001a\u00020\u000eH\u0014J\u0006\u0010\u0019\u001a\u00020\u000eJ\u0006\u0010\u001a\u001a\u00020\u000eR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0006\u001a\u0004\u0018\u00010\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0004\n\u0002\u0010\nR\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001c"}, d2 = {"Lcom/thedasagroup/suminative/scan/MainActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "<init>", "()V", "mHandler", "Landroid/os/Handler;", "scanInterface", "Lcom/sumni/scanner/IScanInterface;", "br", "Landroid/content/BroadcastReceiver;", "Landroid/content/BroadcastReceiver;", "conn", "Landroid/content/ServiceConnection;", "bindScannerService", "", "unbindScannerService", "doScanInterface", "runnable", "Ljava/lang/Runnable;", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onResume", "onPause", "onDestroy", "onScan", "onCameraStop", "Companion", "app_stagingSumniMobilePOSDebug"})
public final class MainActivity extends androidx.appcompat.app.AppCompatActivity {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TAG = "Scan-Test";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_DATA_CODE_RECEIVED = "com.sunmi.scanner.ACTION_DATA_CODE_RECEIVED";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DATA = "data";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String SOURCE = "source_byte";
    public static final int NONE = 100;
    @org.jetbrains.annotations.NotNull()
    private android.os.Handler mHandler;
    @org.jetbrains.annotations.Nullable()
    private com.sumni.scanner.IScanInterface scanInterface;
    @org.jetbrains.annotations.NotNull()
    private final android.content.BroadcastReceiver br = null;
    @org.jetbrains.annotations.NotNull()
    private final android.content.ServiceConnection conn = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.scan.MainActivity.Companion Companion = null;
    
    public MainActivity() {
        super();
    }
    
    private final void bindScannerService() {
    }
    
    private final void unbindScannerService() {
    }
    
    private final void doScanInterface(java.lang.Runnable runnable) {
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    @android.annotation.SuppressLint(value = {"UnspecifiedRegisterReceiverFlag"})
    protected void onResume() {
    }
    
    @java.lang.Override()
    protected void onPause() {
    }
    
    @java.lang.Override()
    protected void onDestroy() {
    }
    
    public final void onScan() {
    }
    
    public final void onCameraStop() {
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/thedasagroup/suminative/scan/MainActivity$Companion;", "", "<init>", "()V", "TAG", "", "ACTION_DATA_CODE_RECEIVED", "DATA", "SOURCE", "NONE", "", "app_stagingSumniMobilePOSDebug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}