{"className": "com.thedasagroup.suminative.ui.tracking.TrackingActivity", "classAnnotations": ["androidx.compose.runtime.internal.StabilityInferred"], "interfaces": ["android.content.ComponentCallbacks", "android.content.ComponentCallbacks2", "android.view.KeyEvent$Callback", "android.view.LayoutInflater$Factory", "android.view.LayoutInflater$Factory2", "android.view.View$OnCreateContextMenuListener", "android.view.Window$Callback", "androidx.activity.FullyDrawnReporterOwner", "androidx.activity.OnBackPressedDispatcherOwner", "androidx.activity.contextaware.ContextAware", "androidx.activity.result.ActivityResultCaller", "androidx.activity.result.ActivityResultRegistryOwner", "androidx.appcompat.app.ActionBarDrawerToggle$DelegateProvider", "androidx.appcompat.app.AppCompatCallback", "androidx.core.app.ActivityCompat$OnRequestPermissionsResultCallback", "androidx.core.app.ActivityCompat$RequestPermissionsRequestCodeValidator", "androidx.core.app.OnMultiWindowModeChangedProvider", "androidx.core.app.OnNewIntentProvider", "androidx.core.app.OnPictureInPictureModeChangedProvider", "androidx.core.app.OnUserLeaveHintProvider", "androidx.core.app.TaskStackBuilder$SupportParentable", "androidx.core.content.OnConfigurationChangedProvider", "androidx.core.content.OnTrimMemoryProvider", "androidx.core.view.KeyEventDispatcher$Component", "androidx.core.view.MenuHost", "androidx.lifecycle.HasDefaultViewModelProviderFactory", "androidx.lifecycle.LifecycleOwner", "androidx.lifecycle.ViewModelStoreOwner", "androidx.savedstate.SavedStateRegistryOwner"], "superClasses": ["androidx.appcompat.app.AppCompatActivity", "androidx.fragment.app.FragmentActivity", "androidx.activity.ComponentActivity", "androidx.core.app.ComponentActivity", "android.app.Activity", "android.view.ContextThemeWrapper", "android.content.ContextWrapper", "android.content.Context", "java.lang.Object"]}