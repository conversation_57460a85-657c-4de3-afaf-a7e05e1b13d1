package com.thedasagroup.suminative.ui.reservations;

@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u0000 \u00102\u00020\u00012\u00020\u0002:\u0001\u0010B\u0007\u00a2\u0006\u0004\b\u0003\u0010\u0004J\u0012\u0010\u000b\u001a\u00020\f2\b\u0010\r\u001a\u0004\u0018\u00010\u000eH\u0014J\b\u0010\u000f\u001a\u00020\fH\u0016R\u001b\u0010\u0005\u001a\u00020\u00068FX\u0086\u0084\u0002\u00a2\u0006\f\n\u0004\b\t\u0010\n\u001a\u0004\b\u0007\u0010\b\u00a8\u0006\u0011"}, d2 = {"Lcom/thedasagroup/suminative/ui/reservations/AreaTableSelectionActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "Lcom/airbnb/mvrx/MavericksView;", "<init>", "()V", "viewModel", "Lcom/thedasagroup/suminative/ui/reservations/ReservationsViewModel;", "getViewModel", "()Lcom/thedasagroup/suminative/ui/reservations/ReservationsViewModel;", "viewModel$delegate", "Lkotlin/Lazy;", "onCreate", "", "savedInstanceState", "Landroid/os/Bundle;", "invalidate", "Companion", "app_stagingSumniPos2Debug"})
public final class AreaTableSelectionActivity extends androidx.appcompat.app.AppCompatActivity implements com.airbnb.mvrx.MavericksView {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy viewModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_SELECTED_AREA_ID = "selected_area_id";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_SELECTED_AREA_NAME = "selected_area_name";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_SELECTED_TABLE_ID = "selected_table_id";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_SELECTED_TABLE_NAME = "selected_table_name";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_SELECTED_TABLE_CAPACITY = "selected_table_capacity";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_EXCLUDED_TABLE_IDS = "excluded_table_ids";
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.reservations.AreaTableSelectionActivity.Companion Companion = null;
    
    public AreaTableSelectionActivity() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.ui.reservations.ReservationsViewModel getViewModel() {
        return null;
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    public void invalidate() {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <T extends java.lang.Object>kotlinx.coroutines.Job collectLatest(@org.jetbrains.annotations.NotNull()
    kotlinx.coroutines.flow.Flow<? extends T> $this$collectLatest, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super T, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.airbnb.mvrx.MavericksViewInternalViewModel getMavericksViewInternalViewModel() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String getMvrxViewId() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public androidx.lifecycle.LifecycleOwner getSubscriptionLifecycleOwner() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, T extends java.lang.Object>kotlinx.coroutines.Job onAsync(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onAsync, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends com.airbnb.mvrx.Async<? extends T>> asyncProp, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super java.lang.Throwable, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> onFail, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super T, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> onSuccess) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super S, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super A, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super A, ? super B, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function4<? super A, ? super B, ? super C, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function5<? super A, ? super B, ? super C, ? super D, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object, E extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends E> prop5, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function6<? super A, ? super B, ? super C, ? super D, ? super E, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object, E extends java.lang.Object, F extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends E> prop5, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends F> prop6, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function7<? super A, ? super B, ? super C, ? super D, ? super E, ? super F, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public <S extends com.airbnb.mvrx.MavericksState, A extends java.lang.Object, B extends java.lang.Object, C extends java.lang.Object, D extends java.lang.Object, E extends java.lang.Object, F extends java.lang.Object, G extends java.lang.Object>kotlinx.coroutines.Job onEach(@org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.MavericksViewModel<S> $this$onEach, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends A> prop1, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends B> prop2, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends C> prop3, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends D> prop4, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends E> prop5, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends F> prop6, @org.jetbrains.annotations.NotNull()
    kotlin.reflect.KProperty1<S, ? extends G> prop7, @org.jetbrains.annotations.NotNull()
    com.airbnb.mvrx.DeliveryMode deliveryMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function8<? super A, ? super B, ? super C, ? super D, ? super E, ? super F, ? super G, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
        return null;
    }
    
    @java.lang.Override()
    public void postInvalidate() {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.airbnb.mvrx.UniqueOnly uniqueOnly(@org.jetbrains.annotations.Nullable()
    java.lang.String customId) {
        return null;
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u001e\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u000e\b\u0002\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00110\u0010R\u000e\u0010\u0004\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0005X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/thedasagroup/suminative/ui/reservations/AreaTableSelectionActivity$Companion;", "", "<init>", "()V", "EXTRA_SELECTED_AREA_ID", "", "EXTRA_SELECTED_AREA_NAME", "EXTRA_SELECTED_TABLE_ID", "EXTRA_SELECTED_TABLE_NAME", "EXTRA_SELECTED_TABLE_CAPACITY", "EXTRA_EXCLUDED_TABLE_IDS", "createIntent", "Landroid/content/Intent;", "context", "Landroid/content/Context;", "excludedTableIds", "", "", "app_stagingSumniPos2Debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.content.Intent createIntent(@org.jetbrains.annotations.NotNull()
        android.content.Context context, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.Integer> excludedTableIds) {
            return null;
        }
    }
}