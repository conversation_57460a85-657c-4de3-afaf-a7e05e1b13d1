{"className": "com.thedasagroup.suminative.DaggerApp_HiltComponents_SingletonC$ActivityCImpl", "classAnnotations": [], "interfaces": ["com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersActivity_GeneratedInjector", "com.thedasagroup.suminative.ui.payment.CashPaymentActivity_GeneratedInjector", "com.thedasagroup.suminative.ui.payment.PaymentActivity_GeneratedInjector", "com.thedasagroup.suminative.ui.payment.SumUpPaymentActivity_GeneratedInjector", "com.thedasagroup.suminative.ui.refund.RefundSumUpActivity_GeneratedInjector", "com.thedasagroup.suminative.ui.reservations.AreaTableSelectionActivity_GeneratedInjector", "com.thedasagroup.suminative.ui.settings.SettingsActivity_GeneratedInjector", "dagger.hilt.android.components.ActivityComponent", "dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories$ActivityEntryPoint", "dagger.hilt.android.internal.lifecycle.HiltViewModelFactory$ActivityCreatorEntryPoint", "dagger.hilt.android.internal.lifecycle.HiltWrapper_HiltViewModelFactory_ActivityCreatorEntryPoint", "dagger.hilt.android.internal.managers.FragmentComponentManager$FragmentComponentBuilderEntryPoint", "dagger.hilt.android.internal.managers.ViewComponentManager$ViewComponentBuilderEntryPoint", "dagger.hilt.internal.GeneratedComponent"], "superClasses": ["com.thedasagroup.suminative.App_HiltComponents$ActivityC", "java.lang.Object"]}