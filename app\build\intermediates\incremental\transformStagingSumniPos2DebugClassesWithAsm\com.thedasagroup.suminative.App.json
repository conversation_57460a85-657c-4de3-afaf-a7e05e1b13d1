{"className": "com.thedasagroup.suminative.App", "classAnnotations": ["dagger.hilt.android.HiltAndroidApp", "androidx.compose.runtime.internal.StabilityInferred"], "interfaces": ["android.app.Application$ActivityLifecycleCallbacks", "android.content.ComponentCallbacks", "android.content.ComponentCallbacks2", "androidx.work.Configuration$Provider"], "superClasses": ["android.app.Application", "android.content.ContextWrapper", "android.content.Context", "java.lang.Object"]}