<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.dasadirect.dasapos2"
    android:versionCode="79"
    android:versionName="2.22" >

    <uses-sdk
        android:minSdkVersion="26"
        android:targetSdkVersion="34" />

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!-- <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

    <queries>
        <package android:name="woyou.aidlservice.jiuiv5" />

        <intent>
            <action android:name="android.intent.action.VIEW" />

            <category android:name="android.intent.category.BROWSABLE" />

            <data android:scheme="https" />
        </intent>
    </queries>
    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <category android:name="android.intent.category.BROWSABLE" />

            <data android:scheme="https" />
        </intent>
    </queries>

    <!--
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" tools:node="remove"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    -->


    <!-- SumUp SDK Permissions -->
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_CONNECT"
        android:usesPermissionFlags="neverForLocation" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.location.gps"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.location.network"
        android:required="false" /> <!-- Also implied, but also really needed -->
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.screen.portrait"
        android:required="true" /> <!-- any location is good enough for us -->
    <uses-feature
        android:name="android.hardware.location"
        android:required="true" /> <!-- Only necessary because of missing checks. See: APPS-801 -->
    <uses-feature
        android:name="android.hardware.bluetooth"
        android:required="true" />

    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <permission
        android:name="com.dasadirect.dasapos2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.dasadirect.dasapos2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.thedasagroup.suminative.App"
        android:allowBackup="false"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.SumiNative"
        android:usesCleartextTraffic="true" >
        <receiver
            android:name="com.thedasagroup.suminative.ui.service.StartReceiver"
            android:enabled="true"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.thedasagroup.suminative.ui.service.EndlessSocketService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse" >
        </service>

        <activity
            android:name="com.thedasagroup.suminative.ui.stores.SelectStoreActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:screenOrientation="landscape"
            android:theme="@style/Theme.SumiNative" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.thedasagroup.suminative.ui.login.LoginActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.thedasagroup.suminative.ui.user_profile.SelectUserProfileActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.thedasagroup.suminative.ui.MainActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.thedasagroup.suminative.ui.tracking.TrackingActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.thedasagroup.suminative.ui.stores.ClosedStoreActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.thedasagroup.suminative.ui.stock.StockActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.thedasagroup.suminative.ui.sales.SalesActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.thedasagroup.suminative.LCDActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.thedasagroup.suminative.ui.payment.PaymentActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.thedasagroup.suminative.ui.payment.CashPaymentActivity"
            android:launchMode="singleTop"
            android:screenOrientation="landscape"
            android:theme="@style/Theme.SumiNative" />
        <activity
            android:name="com.thedasagroup.suminative.ui.splitbill.SplitBillActivity"
            android:launchMode="singleTop"
            android:screenOrientation="landscape"
            android:theme="@style/Theme.SumiNative" />
        <activity
            android:name="com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.thedasagroup.suminative.ui.refund.RefundSumUpActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.thedasagroup.suminative.ui.local_orders.LocalOrdersActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.thedasagroup.suminative.ui.reservations.ReservationsActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.thedasagroup.suminative.ui.rewards.RewardsActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.thedasagroup.suminative.ui.reservations.AreaTableSelectionActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.thedasagroup.suminative.ui.reservations.AreaTableSelectionUsageExample"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.thedasagroup.suminative.ui.settings.SettingsActivity"
            android:exported="false"
            android:label="Settings"
            android:parentActivityName="com.thedasagroup.suminative.ui.MainActivity" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".ui.MainActivity" />
        </activity>

        <!-- SumUp Payment Activity -->
        <activity
            android:name="com.thedasagroup.suminative.ui.payment.SumUpPaymentActivity"
            android:exported="false"
            android:theme="@style/Theme.SumiNative" />
        <activity
            android:name="com.pluto.ui.selector.SelectorActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/PlutoTheme.Selector" />
        <activity
            android:name="com.pluto.ui.container.PlutoActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/PlutoContainerTheme"
            android:windowSoftInputMode="stateUnspecified|adjustResize" />
        <activity
            android:name="com.pluto.tool.modules.ruler.RulerActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/PlutoContainerTheme" />

        <provider
            android:name="com.pluto.core.PlutoFileProvider"
            android:authorities="pluto___com.dasadirect.dasapos2.provider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/pluto___file_provider_paths" />
        </provider>

        <activity
            android:name="com.sumup.merchant.reader.identitylib.ui.activities.LoginActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="locked"
            android:theme="@style/SumUpTheme.ActionBarNoShadow"
            android:windowSoftInputMode="adjustResize" >
        </activity>
        <activity
            android:name="com.sumup.merchant.reader.identitylib.ui.activities.ssologin.SSOLoginActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="locked"
            android:theme="@style/SumUpTheme.ActionBarNoShadow"
            android:windowSoftInputMode="adjustResize" >
        </activity>
        <activity
            android:name="com.sumup.merchant.reader.ui.activities.CardReaderPaymentAPIDrivenPageActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="locked"
            android:theme="@style/SumUpTheme.NoActionBar"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name="com.sumup.merchant.reader.ui.activities.PaymentSettingsActivity"
            android:label="@string/sumup_payment_setting_card_reader"
            android:screenOrientation="locked"
            android:theme="@style/SumUpTheme" />
        <activity
            android:name="com.sumup.merchant.reader.ui.activities.CardReaderSetupActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="locked"
            android:theme="@style/SumUpTheme.ActionBarNoShadow" />
        <activity
            android:name="com.sumup.merchant.reader.troubleshooting.ui.BtTroubleshootingActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="locked"
            android:theme="@style/SumUpTheme.ActionBarNoShadow" />
        <activity
            android:name="com.sumup.merchant.reader.troubleshooting.ReaderTroubleshootingActivity"
            android:screenOrientation="locked"
            android:theme="@style/SumUpTheme.NoActionBar" />
        <activity
            android:name="com.sumup.merchant.reader.webview.ReaderWebViewActivity"
            android:screenOrientation="locked"
            android:theme="@style/SumUpTheme.NoActionBar" />
        <activity
            android:name="com.sumup.merchant.reader.autoreceipt.AutoReceiptSettingsActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="locked"
            android:theme="@style/SumUpTheme.ActionBarNoShadow"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name="com.sumup.merchant.reader.ui.activities.CardReaderPageActivity"
            android:label="@string/sumup_payment_setting_card_reader"
            android:screenOrientation="locked"
            android:theme="@style/SumUpTheme.ActionBarNoShadow" />

        <receiver
            android:name="com.sumup.merchant.reader.receiver.ShareReceiptReceiver"
            android:exported="false" /> <!-- This is exported so users can launch it from the command line. It should only be included in debug builds. -->
        <activity
            android:name="com.airbnb.mvrx.launcher.MavericksLauncherActivity"
            android:exported="true" />
        <activity android:name="com.airbnb.mvrx.launcher.MavericksLauncherMockActivity" />
        <activity android:name="com.airbnb.mvrx.launcher.MavericksLauncherTestMocksActivity" />
        <activity
            android:name="net.openid.appauth.AuthorizationManagementActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation|keyboard|keyboardHidden"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/Theme.AppCompat.Translucent.NoTitleBar" />
        <activity
            android:name="net.openid.appauth.RedirectUriReceiverActivity"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="com.dasadirect.dasapos2" />
            </intent-filter>
        </activity>

        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:directBootAware="true"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <activity
            android:name="androidx.activity.ComponentActivity"
            android:exported="true" />
        <activity
            android:name="androidx.compose.ui.tooling.PreviewActivity"
            android:exported="true" />

        <service
            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" />
        <service
            android:name="androidx.work.impl.background.systemjob.SystemJobService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_job_service_default"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_foreground_service_default"
            android:exported="false" />

        <receiver
            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="false" />
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BATTERY_OKAY" />
                <action android:name="android.intent.action.BATTERY_LOW" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.TIME_SET" />
                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" >
            <intent-filter>
                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
            </intent-filter>
        </receiver>

        <property
            android:name="android.adservices.AD_SERVICES_CONFIG"
            android:resource="@xml/ga_ad_services_config" />

        <service
            android:name="com.google.firebase.sessions.SessionLifecycleService"
            android:enabled="true"
            android:exported="false" />

        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:authorities="com.dasadirect.dasapos2.firebaseinitprovider"
            android:directBootAware="true"
            android:exported="false"
            android:initOrder="100" />

        <receiver
            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
            android:enabled="true"
            android:exported="false" >
        </receiver>

        <service
            android:name="com.google.android.gms.measurement.AppMeasurementService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <uses-library
            android:name="android.ext.adservices"
            android:required="false" />
        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" />
        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false" >
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct" />
        </service>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" >
        </service>

        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false" />
    </application>

</manifest>