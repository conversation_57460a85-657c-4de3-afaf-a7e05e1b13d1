package com.thedasagroup.suminative.ui.rewards

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.ui.Modifier
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.viewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class RewardsActivity : ComponentActivity(), MavericksView {
    
    private val viewModel: RewardsViewModel by viewModel()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            RewardsScreen(
                viewModel = viewModel,
                onBackClick = {
                    finish()
                },
                modifier = Modifier.fillMaxSize()
            )
        }
    }
    
    override fun invalidate() {
        // Required by MavericksView
    }
}
