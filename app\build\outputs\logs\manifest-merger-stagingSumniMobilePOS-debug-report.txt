-- Merging decision tree log ---
provider#androidx.startup.InitializationProvider
INJECTED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:34:9-152
REJECTED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:34:9-152
REJECTED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\f5f1425d1e4978261a17995de2f2e6c7\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\c6823265c260db11438a1b42512fafe7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\c6823265c260db11438a1b42512fafe7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5a03c8148dda9d2dd4527bd6d753fe4f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5a03c8148dda9d2dd4527bd6d753fe4f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:148:13-32
	android:authorities
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:147:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:146:13-67
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:2:1-57:12
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:2:1-57:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:2:1-168:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:2:1-168:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:2:1-168:12
MERGED from [app.cash.sqldelight:android-driver:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\b68bbe5ab5dd12a63d1b11c8565c45f2\transformed\android-driver-2.1.0-debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:2:1-40:12
MERGED from [com.plutolib.plugins:network-interceptor-ktor:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\6b908d9705f6f67039640d181a8b1bed\transformed\network-interceptor-ktor-2.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.plutolib.plugins:network:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c043c0a96b5d13f9a89fdab6a115e758\transformed\network-2.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.markowanga:timber-logging-to-file:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\2158565f2a07fed0f4de652a21cba132\transformed\timber-logging-to-file-1.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:2:1-119:12
MERGED from [com.plutolib:plugin:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\f342587e8a85ae406fb7edc279b6f26a\transformed\plugin-2.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:2:1-17:12
MERGED from [com.airbnb.android:epoxy:5.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\b1297ece4cd40dea7767ec9168c1b1c4\transformed\epoxy-5.0.0-beta05\AndroidManifest.xml:2:1-11:12
MERGED from [com.sumup:txresult:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\9634aa87d5bfe5bca57e591edea4bbb7\transformed\txresult-5.0.3\AndroidManifest.xml:2:1-9:12
MERGED from [com.sumup:base-network:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\32c2db6455ae97d1123fca02ba629ebd\transformed\base-network-5.0.3\AndroidManifest.xml:2:1-9:12
MERGED from [com.sumup:reader-core:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bc19d6974d59a10f05a54255bd439894\transformed\reader-core-5.0.3\AndroidManifest.xml:2:1-25:12
MERGED from [com.sumup:base-analytics:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\c44a39f5f00685f0097f3bcd4bd71c82\transformed\base-analytics-5.0.3\AndroidManifest.xml:2:1-9:12
MERGED from [com.sumup:receipts-core:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\69ee6742af239ab0f65c33fdde79bb6e\transformed\receipts-core-5.0.3\AndroidManifest.xml:2:1-9:12
MERGED from [com.sumup:base-common:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\9af35c9b69a836668ca10d4a34a22b95\transformed\base-common-5.0.3\AndroidManifest.xml:2:1-9:12
MERGED from [com.sumup.designlib:solo-ui:2.13.4] C:\Users\<USER>\.gradle\caches\transforms-4\ca1960b188168d2365c788611c5b146a\transformed\solo-ui-2.13.4\AndroidManifest.xml:2:1-9:12
MERGED from [com.sumup.designlib:circuit-ui:2.13.4] C:\Users\<USER>\.gradle\caches\transforms-4\222f4099276c0321d91628bf12146474\transformed\circuit-ui-2.13.4\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\56f1fdbf647eaff96ebf00a56cd72a54\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.airbnb.android:mavericks-hilt:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\af1026a0ab98941e7f940c3cda08bd71\transformed\mavericks-hilt-3.0.10\AndroidManifest.xml:2:1-9:12
MERGED from [com.airbnb.android:mavericks-compose:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\0d9a447f64c7373e913895411120ace9\transformed\mavericks-compose-3.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.airbnb.android:mavericks-mocking:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\a282ebe719910e4521db7acfd87744b6\transformed\mavericks-mocking-3.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.airbnb.android:mavericks:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\a202970d2b460f0d6cb3c2f03f7cd6b0\transformed\mavericks-3.0.10\AndroidManifest.xml:2:1-9:12
MERGED from [com.afollestad.material-dialogs:core:3.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4e2749c3ad3a5cb5c91ad98d8e64ef2f\transformed\core-3.3.0\AndroidManifest.xml:2:1-13:12
MERGED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:15:1-55:12
MERGED from [com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:3:1-23:12
MERGED from [io.coil-kt:coil-compose:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\815c4183bcf53be1936776a0c952b550\transformed\coil-compose-2.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\27304d73bf324a574309a7cb83a69ad6\transformed\coil-compose-base-2.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\1142f6c10186b0585dd32ff4da1f5ce9\transformed\coil-2.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8affc95c1bf0ab492f449c134f4964e7\transformed\coil-base-2.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\7ac99e26b185333d178a61f77db65d14\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\6bbc4a5c4ed63500292083a1ffc24782\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.sumup.identity.auth:auth-sdk:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f2e7f4b5c6199450632daed8f9628a8\transformed\auth-sdk-1.1.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\208a76efc944b3a8edf00fa399129d7c\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [dev.shreyaspatil:capturable:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\add5ced8675b6385ee3a60caf6a72871\transformed\capturable-2.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\32d8c721da82ee1c20f5db3a0f93b84d\transformed\webkit-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d352988101819e006c7e123d3310a04a\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c0641230338978debab761d8c8e2b23b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-common:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\51d42a11970881847f3eeebd9b1e6b8b\transformed\navigation-common-2.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\42074f75890724836a12c77a021c7309\transformed\navigation-runtime-2.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\624b35058fa918535e64f795c486f9b3\transformed\navigation-common-ktx-2.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\622fb3051c19ddeb57cc108a31b47e50\transformed\navigation-compose-2.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\ecd42d0c15ba3ec37edf07b45f590e64\transformed\navigation-runtime-ktx-2.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\561280f565be1bec57ded9bb7b820573\transformed\navigation-fragment-ktx-2.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\c790a3968d30b5581f46bf141c117b9f\transformed\navigation-fragment-2.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2c4df0049fe6df3838f79bb92a798f00\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\5fc81b5c467f15efbd01ee9e2907da29\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e15e63b63af9fe16b8933315b533f667\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\5b5a50e8d8f224136f03155ef0368325\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\59afdd34d799c7747d219ce6ea7bb4e5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cde5d26f4bcf9a86c2bd631a553016d5\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.Kaaveh:sdp-compose:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab5e4eb6bbbe62ecbf705b99dd0eaf70\transformed\sdp-compose-1.1.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-4\95e6a4d262e95b281c51df792e8ba726\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-swiperefresh:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-4\3294670c912048148491d10ca25611e3\transformed\accompanist-swiperefresh-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bafe51bd12d3646532745d656825df70\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\06e375c8434421977ca4b1505356fc07\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\728f960a684d825030c619518a6e897c\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\a3553c94ffb9a42d7bcbbd348903785f\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\340cc32149b67e7dcbdaa675121309be\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\a5d9350484b77cf3f3146c53b5d0ada1\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\9a62ad56e67be1b884ce7cee9835181b\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\0066e3b84593ce92592adad4454c96f0\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\c73503d83fa506d0fb42705327fe45b2\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\aa8638deccfdc8890effb9d7aeceb2f4\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\dc6b599d38b08540c8eb079d24ba226b\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\6c73fdb2ccf16d86ef3201dbf8616f3b\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\4795aa3da7ed929ac4b15ceb6adcbbc3\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\6d58ad3ab456bc882a3f1d48b60ed688\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\af419de7e09ee031d89b8c3f5b39decc\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\29548e48d3d583e289c0c341f6c53f29\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-work:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0b9620b9cdb8d535c49b46138a4cca\transformed\hilt-work-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.dagger:hilt-android:2.56.2] C:\Users\<USER>\.gradle\caches\transforms-4\0a4f64c7e32c255233ed24a394a9887f\transformed\hilt-android-2.56.2\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\de526a0578e170795fbc7ce4dc7819f5\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\f5f1425d1e4978261a17995de2f2e6c7\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\682c0a3ed307cb49200ad76c1f3d1a02\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:17:1-145:12
MERGED from [com.sumup:mvp-base:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\4c82b3468554e74b56c5522122bd7d83\transformed\mvp-base-5.0.3\AndroidManifest.xml:2:1-9:12
MERGED from [com.evernote:android-state:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\cb8995334c259f73a6d8a8541024248e\transformed\android-state-1.4.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:17:1-38:12
MERGED from [com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-analytics:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\de037d029c76ad8accf275de500ba0c9\transformed\firebase-analytics-22.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\7adf4ef68603e1308d9f276765d91c42\transformed\play-services-measurement-sdk-22.0.2\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcc3b72f95eba0d0cce8148810ef28fd\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6e202ec09164f6a885f9b3783eccd125\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\47a665339a63fda627234c653a81445e\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6240e726c4ef5144f3f52d5eb6e27fe1\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\ad5685441e117ff27d85994c3aa70de2\transformed\play-services-measurement-base-22.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b55258aaf11c7a2036372a533994273f\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\034f994bdbf2fd07ef059a1448498b08\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:2:1-19:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\7eb61bae09a54e29940b5eb3e36af850\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\ea75c8b7164fb774e3850e4088a40e23\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\d1b1aa02e585c9e330687a6c817500d7\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\58ccdb203ab22904b746d96ab66e8647\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\be8e6d03124325b264866665fed17104\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\f900e6ad2ae88254f6a07ac00b8c978b\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\e8a76250a913914146b7dd1a2cfbe5b6\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e62476b4c042791edd431f8ea302dd9\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8ef32b97d33e76babbf431fc8353821a\transformed\datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\df3ff725c0bedb457d599b96e7dce2e8\transformed\room-ktx-2.5.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f05eaf7ef0f2c9015e659e5823b82477\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0306bc89db6006d4ec5c8eebf598197a\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\41fb373b4b32a0fb5849622bc0f36577\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\f65688050329197f766babfe8912e4cb\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\096a1b28bbf75406389417c577f11255\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb50bf294701163d417e38ed66bc7049\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a935ea9f2ba4179b305800bbe11ad9e2\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\d0077d2d29f12c8f8eb5f26f601033cf\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\252f4bab0a253411bfa4a5af35bf4301\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\777c584b16222d17d9b9a54b491e057f\transformed\lifecycle-service-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\33aae88c87a3d599ffb21369127bdcda\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\c6823265c260db11438a1b42512fafe7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\d90e730951dba7c83dd1ff68583aab59\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\ed3fddc32dad58d5f5fed01fe8b1384c\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\fba83fde3eaab70ccedeecf40d34fc80\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\66b89678b56d49beb9dd4c28762514bc\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\b890659f6db25116dd25448a00dbe8e9\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\824e1d4e670b8eeb1b49deaac1109b38\transformed\activity-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\251660c5ae582475e6db33f00f203beb\transformed\activity-compose-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\d25e8fd67c69471399a2da8edbbfba6b\transformed\activity-1.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\955c7711c7167e9d93b196a9bf4006fc\transformed\recyclerview-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\914acba902d7170e7c627b3e54c32c13\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3e3f9b8282e213922209b79308a66875\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bff0aab0a70f73322b2817fb523103ec\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\57407fb10e0cea29c758140c4fd27bcf\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ded531c7bbfa1d22dd903befcfd57dc7\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\9846d17c2cc1b944e646718462860232\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac23b210ee0bffdcc05003d93de6f576\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\7e2a3544b05530d413740856296e4693\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.kittinunf.fuel:fuel-android:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\348a6e29e36da3f011773176cceb4cad\transformed\fuel-android-2.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\172774615e637f789a4acaaba37e4492\transformed\timber-5.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:17:1-31:12
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\2fc19c94460304c62776bba88cc9ef3d\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:15:1-33:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\85608ab3fe37796046b1e9073d518576\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:15:1-22:12
MERGED from [androidx.sqlite:sqlite-android:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\a007e3c3ca0389380a15a448c90e1fab\transformed\sqlite-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ef0870a61f83553e7109b7d080e61337\transformed\sqlite-framework-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\9bc6b8814ebcb771e44a4775ac7f3cc3\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\a796013ccb4df496d75abd5756240bf6\transformed\viewbinding-7.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a86dcca990bb293108a9c8bee7355bf7\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5011e8de47b10838518eebd977c31e8a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [com.sumup:stub:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\e0814801a626399b803268b7728505d1\transformed\stub-5.0.3\AndroidManifest.xml:2:1-9:12
MERGED from [com.sumup:contract:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\62f8d1bc710e35614c86a3fa8a4bb71e\transformed\contract-5.0.3\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\829dd5b517eefafa5a4e21d216fcf925\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\05eb7925ef580759c0e09a53846d9c5b\transformed\transport-api-3.0.0\AndroidManifest.xml:15:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1ddf191b25600d353b0d7f4d95e4b65c\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\039e2ba24c1340d178a7afc5c69bfb7e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5a03c8148dda9d2dd4527bd6d753fe4f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f17619d1742aa543018e2fd7b7bdc690\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bbe84aa922352f985d8a62c3c84ac18a\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\159a5f5f98e3bb7a6c1c7628c3d607d4\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c36ce1576d1dd2308b47daab5ede721\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\91e4375c2d178908431c4fb5be28d0f7\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-4\27e2abdd3eba7239490b45a12f269d49\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.sunmi:printerlibrary:1.0.24] C:\Users\<USER>\.gradle\caches\transforms-4\173b165c816c25298f6e35dd0e9b44f6\transformed\printerlibrary-1.0.24\AndroidManifest.xml:2:1-9:12
MERGED from [com.sunmi:printerx:1.0.18] C:\Users\<USER>\.gradle\caches\transforms-4\f23f023e1ff1eb4fc746e096e9d5c330\transformed\printerx-1.0.18\AndroidManifest.xml:2:1-13:12
MERGED from [com.intuit.sdp:sdp-android:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d6dabfb88d99960db32b859140a585ce\transformed\sdp-android-1.1.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.dagger:dagger-lint-aar:2.56.2] C:\Users\<USER>\.gradle\caches\transforms-4\59415d251cc0cb57b8c631f2b6d6bae2\transformed\dagger-lint-aar-2.56.2\AndroidManifest.xml:16:1-19:12
MERGED from [com.sumup.android:logging:2.2.4] C:\Users\<USER>\.gradle\caches\transforms-4\c94a969af31a811d38b7871102ab72af\transformed\logging-2.2.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3a571746a853aab84afaefd7c9e44e9\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.sumup.analyticskit:core:0.0.19] C:\Users\<USER>\.gradle\caches\transforms-4\41caceea033f3e46deb5690152d3516e\transformed\core-0.0.19\AndroidManifest.xml:2:1-9:12
MERGED from [org.greenrobot:eventbus:3.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\6941a24d1091c51714d1fd6238532062\transformed\eventbus-3.3.1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:5:5-76
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:5:5-76
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:5:5-76
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:5:22-74
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:6:5-66
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:6:5-66
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:6:5-66
MERGED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:10:5-67
MERGED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:10:5-67
MERGED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:22:5-67
MERGED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6e202ec09164f6a885f9b3783eccd125\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6e202ec09164f6a885f9b3783eccd125\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6240e726c4ef5144f3f52d5eb6e27fe1\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6240e726c4ef5144f3f52d5eb6e27fe1\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:10:5-67
MERGED from [com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:10:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:7:5-68
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:7:5-68
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:7:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6e202ec09164f6a885f9b3783eccd125\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6e202ec09164f6a885f9b3783eccd125\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6240e726c4ef5144f3f52d5eb6e27fe1\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6240e726c4ef5144f3f52d5eb6e27fe1\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:25:5-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:7:22-65
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:8:5-79
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:8:5-79
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:8:5-79
MERGED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:11:5-79
MERGED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:11:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6e202ec09164f6a885f9b3783eccd125\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6e202ec09164f6a885f9b3783eccd125\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6240e726c4ef5144f3f52d5eb6e27fe1\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6240e726c4ef5144f3f52d5eb6e27fe1\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:11:5-79
MERGED from [com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:11:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:8:22-76
uses-permission#android.permission.FOREGROUND_SERVICE_SPECIAL_USE
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:10:5-88
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:10:5-88
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:10:5-88
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:10:22-86
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:11:5-107
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:11:5-107
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:11:5-107
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:11:78-104
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:11:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:12:5-108
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:12:5-108
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:12:5-108
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:12:79-105
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:12:22-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:13:5-76
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:13:5-76
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:13:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:13:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:14:5-75
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:14:5-75
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:14:5-75
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:14:22-72
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:15:5-75
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:15:5-75
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:15:5-75
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:15:22-72
uses-permission#android.permission.BLUETOOTH
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:18:5-68
MERGED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:14:5-16:38
MERGED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:14:5-16:38
MERGED from [com.sumup:reader-core:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bc19d6974d59a10f05a54255bd439894\transformed\reader-core-5.0.3\AndroidManifest.xml:10:5-12:38
MERGED from [com.sumup:reader-core:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bc19d6974d59a10f05a54255bd439894\transformed\reader-core-5.0.3\AndroidManifest.xml:10:5-12:38
	android:maxSdkVersion
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:16:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:18:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:19:5-74
MERGED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:17:5-19:38
MERGED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:17:5-19:38
MERGED from [com.sumup:reader-core:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bc19d6974d59a10f05a54255bd439894\transformed\reader-core-5.0.3\AndroidManifest.xml:13:5-15:38
MERGED from [com.sumup:reader-core:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bc19d6974d59a10f05a54255bd439894\transformed\reader-core-5.0.3\AndroidManifest.xml:13:5-15:38
	android:maxSdkVersion
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:19:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:19:22-71
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:20:5-76
MERGED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:25:5-27:58
MERGED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:25:5-27:58
MERGED from [com.sumup:reader-core:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bc19d6974d59a10f05a54255bd439894\transformed\reader-core-5.0.3\AndroidManifest.xml:21:5-23:58
MERGED from [com.sumup:reader-core:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bc19d6974d59a10f05a54255bd439894\transformed\reader-core-5.0.3\AndroidManifest.xml:21:5-23:58
	android:usesPermissionFlags
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:27:9-55
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:20:22-73
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:21:5-73
MERGED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:22:5-24:58
MERGED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:22:5-24:58
MERGED from [com.sumup:reader-core:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bc19d6974d59a10f05a54255bd439894\transformed\reader-core-5.0.3\AndroidManifest.xml:18:5-20:58
MERGED from [com.sumup:reader-core:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bc19d6974d59a10f05a54255bd439894\transformed\reader-core-5.0.3\AndroidManifest.xml:18:5-20:58
	android:usesPermissionFlags
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:24:9-55
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:21:22-70
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:5-39:19
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:5-39:19
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:5-39:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:21:5-150:19
MERGED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:11:5-38:19
MERGED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:11:5-38:19
MERGED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:54:5-117:19
MERGED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:54:5-117:19
MERGED from [com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:7:5-15:19
MERGED from [com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:7:5-15:19
MERGED from [com.airbnb.android:epoxy:5.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\b1297ece4cd40dea7767ec9168c1b1c4\transformed\epoxy-5.0.0-beta05\AndroidManifest.xml:9:5-20
MERGED from [com.airbnb.android:epoxy:5.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\b1297ece4cd40dea7767ec9168c1b1c4\transformed\epoxy-5.0.0-beta05\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\56f1fdbf647eaff96ebf00a56cd72a54\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\56f1fdbf647eaff96ebf00a56cd72a54\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.airbnb.android:mavericks-hilt:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\af1026a0ab98941e7f940c3cda08bd71\transformed\mavericks-hilt-3.0.10\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:mavericks-hilt:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\af1026a0ab98941e7f940c3cda08bd71\transformed\mavericks-hilt-3.0.10\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:mavericks:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\a202970d2b460f0d6cb3c2f03f7cd6b0\transformed\mavericks-3.0.10\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:mavericks:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\a202970d2b460f0d6cb3c2f03f7cd6b0\transformed\mavericks-3.0.10\AndroidManifest.xml:7:5-20
MERGED from [com.afollestad.material-dialogs:core:3.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4e2749c3ad3a5cb5c91ad98d8e64ef2f\transformed\core-3.3.0\AndroidManifest.xml:11:5-47
MERGED from [com.afollestad.material-dialogs:core:3.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4e2749c3ad3a5cb5c91ad98d8e64ef2f\transformed\core-3.3.0\AndroidManifest.xml:11:5-47
MERGED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:34:5-53:19
MERGED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:34:5-53:19
MERGED from [com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:10:5-21:19
MERGED from [com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:10:5-21:19
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\6bbc4a5c4ed63500292083a1ffc24782\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\6bbc4a5c4ed63500292083a1ffc24782\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\f5f1425d1e4978261a17995de2f2e6c7\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\f5f1425d1e4978261a17995de2f2e6c7\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-analytics:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\de037d029c76ad8accf275de500ba0c9\transformed\firebase-analytics-22.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\de037d029c76ad8accf275de500ba0c9\transformed\firebase-analytics-22.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\7adf4ef68603e1308d9f276765d91c42\transformed\play-services-measurement-sdk-22.0.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\7adf4ef68603e1308d9f276765d91c42\transformed\play-services-measurement-sdk-22.0.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:10:5-16:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:10:5-16:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcc3b72f95eba0d0cce8148810ef28fd\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcc3b72f95eba0d0cce8148810ef28fd\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6e202ec09164f6a885f9b3783eccd125\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6e202ec09164f6a885f9b3783eccd125\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\47a665339a63fda627234c653a81445e\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\47a665339a63fda627234c653a81445e\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\ad5685441e117ff27d85994c3aa70de2\transformed\play-services-measurement-base-22.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\ad5685441e117ff27d85994c3aa70de2\transformed\play-services-measurement-base-22.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b55258aaf11c7a2036372a533994273f\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b55258aaf11c7a2036372a533994273f\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:17:5-20
MERGED from [com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:17:5-20
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f05eaf7ef0f2c9015e659e5823b82477\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f05eaf7ef0f2c9015e659e5823b82477\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\c6823265c260db11438a1b42512fafe7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\c6823265c260db11438a1b42512fafe7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\172774615e637f789a4acaaba37e4492\transformed\timber-5.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\172774615e637f789a4acaaba37e4492\transformed\timber-5.0.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:23:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\039e2ba24c1340d178a7afc5c69bfb7e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\039e2ba24c1340d178a7afc5c69bfb7e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5a03c8148dda9d2dd4527bd6d753fe4f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5a03c8148dda9d2dd4527bd6d753fe4f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.sumup.android:logging:2.2.4] C:\Users\<USER>\.gradle\caches\transforms-4\c94a969af31a811d38b7871102ab72af\transformed\logging-2.2.4\AndroidManifest.xml:9:5-20
MERGED from [com.sumup.android:logging:2.2.4] C:\Users\<USER>\.gradle\caches\transforms-4\c94a969af31a811d38b7871102ab72af\transformed\logging-2.2.4\AndroidManifest.xml:9:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:217-256
	android:largeHeap
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:401-425
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:149-183
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:257-283
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:184-216
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:103-148
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:324-344
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:18-45
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:284-323
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:46-102
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:365-400
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:345-364
receiver#com.thedasagroup.suminative.ui.service.StartReceiver
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:25:9-29:20
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:25:9-29:20
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:25:9-29:20
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:25:19-41
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:25:83-106
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:25:42-82
intent-filter#action:name:android.intent.action.BOOT_COMPLETED
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:26:13-28:29
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:17-78
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:25-76
service#com.thedasagroup.suminative.ui.service.EndlessSocketService
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:9-32:19
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:9-32:19
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:9-32:19
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:66-88
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:89-113
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:114-156
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:18-65
activity#com.thedasagroup.suminative.ui.payment.SumUpPaymentActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:37:9-134
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:37:67-91
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:37:92-131
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:37:19-66
queries
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:41:5-47:15
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:41:5-47:15
MERGED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:41:5-47:15
MERGED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:24:5-32:15
MERGED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:24:5-32:15
MERGED from [com.sunmi:printerx:1.0.18] C:\Users\<USER>\.gradle\caches\transforms-4\f23f023e1ff1eb4fc746e096e9d5c330\transformed\printerx-1.0.18\AndroidManifest.xml:9:5-11:15
MERGED from [com.sunmi:printerx:1.0.18] C:\Users\<USER>\.gradle\caches\transforms-4\f23f023e1ff1eb4fc746e096e9d5c330\transformed\printerx-1.0.18\AndroidManifest.xml:9:5-11:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:42:9-46:18
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:13-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:21-62
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:13-74
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:23-71
data
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:13-44
	android:scheme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:19-41
package#com.sunmi.scanner
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:18:9-53
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:18:18-50
activity#com.thedasagroup.suminative.ui.stores.SelectStoreActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:51:9-64:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:56:13-49
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:54:13-45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:53:13-36
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:57:13-42
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:55:13-52
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:52:13-58
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:59:13-63:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:60:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:60:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:62:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:62:27-74
activity#com.thedasagroup.suminative.ui.login.LoginActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:66:9-69:15
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:67:13-49
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:68:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:66:19-57
activity#com.thedasagroup.suminative.ui.user_profile.SelectUserProfileActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:70:9-73:15
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:71:13-49
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:72:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:70:19-76
activity#com.thedasagroup.suminative.ui.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:74:9-77:15
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:75:13-49
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:76:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:74:19-50
activity#com.thedasagroup.suminative.ui.tracking.TrackingActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:78:9-81:15
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:79:13-49
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:80:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:78:19-63
activity#com.thedasagroup.suminative.ui.stores.ClosedStoreActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:82:9-85:15
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:83:13-49
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:84:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:82:19-64
activity#com.thedasagroup.suminative.ui.stock.StockActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:86:9-89:15
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:87:13-49
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:88:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:86:19-57
activity#com.thedasagroup.suminative.ui.sales.SalesActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:91:9-94:15
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:92:13-49
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:93:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:91:19-57
activity#com.thedasagroup.suminative.ui.payment.PaymentActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:96:9-99:15
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:97:13-49
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:98:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:96:19-61
activity#com.thedasagroup.suminative.ui.payment.CashPaymentActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:101:9-104:15
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:102:13-49
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:103:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:101:19-65
activity#com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:106:9-109:15
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:107:13-49
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:108:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:106:19-70
activity#com.thedasagroup.suminative.ui.refund.RefundSumUpActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:111:9-114:15
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:112:13-49
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:113:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:111:19-64
activity#com.thedasagroup.suminative.ui.categories.CategoriesActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:116:9-119:15
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:117:13-49
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:118:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:116:19-67
activity#com.thedasagroup.suminative.ui.settings.SettingsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:121:9-130:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:125:13-49
	android:parentActivityName
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:126:13-58
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:124:13-37
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:123:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:122:13-57
meta-data#android.support.PARENT_ACTIVITY
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:127:13-129:52
	android:value
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:129:17-49
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:128:17-63
activity#com.thedasagroup.suminative.ui.stores.DownloadProductsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:132:9-134:45
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:133:13-49
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:134:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:132:19-69
activity#com.thedasagroup.suminative.ui.local_orders.LocalOrdersActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:136:9-138:45
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:137:13-49
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:138:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:136:19-70
activity#com.thedasagroup.suminative.ui.reservations.AreaTableSelectionActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:140:9-143:15
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:141:13-49
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:142:13-42
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:140:19-77
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml
MERGED from [app.cash.sqldelight:android-driver:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\b68bbe5ab5dd12a63d1b11c8565c45f2\transformed\android-driver-2.1.0-debug\AndroidManifest.xml:5:5-44
MERGED from [app.cash.sqldelight:android-driver:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\b68bbe5ab5dd12a63d1b11c8565c45f2\transformed\android-driver-2.1.0-debug\AndroidManifest.xml:5:5-44
MERGED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.plutolib.plugins:network-interceptor-ktor:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\6b908d9705f6f67039640d181a8b1bed\transformed\network-interceptor-ktor-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.plutolib.plugins:network-interceptor-ktor:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\6b908d9705f6f67039640d181a8b1bed\transformed\network-interceptor-ktor-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.plutolib.plugins:network:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c043c0a96b5d13f9a89fdab6a115e758\transformed\network-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.plutolib.plugins:network:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c043c0a96b5d13f9a89fdab6a115e758\transformed\network-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.markowanga:timber-logging-to-file:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\2158565f2a07fed0f4de652a21cba132\transformed\timber-logging-to-file-1.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.markowanga:timber-logging-to-file:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\2158565f2a07fed0f4de652a21cba132\transformed\timber-logging-to-file-1.3.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:6:5-8:41
MERGED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:6:5-8:41
MERGED from [com.plutolib:plugin:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\f342587e8a85ae406fb7edc279b6f26a\transformed\plugin-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.plutolib:plugin:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\f342587e8a85ae406fb7edc279b6f26a\transformed\plugin-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:epoxy:5.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\b1297ece4cd40dea7767ec9168c1b1c4\transformed\epoxy-5.0.0-beta05\AndroidManifest.xml:5:5-7:41
MERGED from [com.airbnb.android:epoxy:5.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\b1297ece4cd40dea7767ec9168c1b1c4\transformed\epoxy-5.0.0-beta05\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup:txresult:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\9634aa87d5bfe5bca57e591edea4bbb7\transformed\txresult-5.0.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup:txresult:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\9634aa87d5bfe5bca57e591edea4bbb7\transformed\txresult-5.0.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup:base-network:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\32c2db6455ae97d1123fca02ba629ebd\transformed\base-network-5.0.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup:base-network:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\32c2db6455ae97d1123fca02ba629ebd\transformed\base-network-5.0.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup:reader-core:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bc19d6974d59a10f05a54255bd439894\transformed\reader-core-5.0.3\AndroidManifest.xml:6:5-8:41
MERGED from [com.sumup:reader-core:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bc19d6974d59a10f05a54255bd439894\transformed\reader-core-5.0.3\AndroidManifest.xml:6:5-8:41
MERGED from [com.sumup:base-analytics:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\c44a39f5f00685f0097f3bcd4bd71c82\transformed\base-analytics-5.0.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup:base-analytics:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\c44a39f5f00685f0097f3bcd4bd71c82\transformed\base-analytics-5.0.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup:receipts-core:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\69ee6742af239ab0f65c33fdde79bb6e\transformed\receipts-core-5.0.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup:receipts-core:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\69ee6742af239ab0f65c33fdde79bb6e\transformed\receipts-core-5.0.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup:base-common:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\9af35c9b69a836668ca10d4a34a22b95\transformed\base-common-5.0.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup:base-common:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\9af35c9b69a836668ca10d4a34a22b95\transformed\base-common-5.0.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup.designlib:solo-ui:2.13.4] C:\Users\<USER>\.gradle\caches\transforms-4\ca1960b188168d2365c788611c5b146a\transformed\solo-ui-2.13.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup.designlib:solo-ui:2.13.4] C:\Users\<USER>\.gradle\caches\transforms-4\ca1960b188168d2365c788611c5b146a\transformed\solo-ui-2.13.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup.designlib:circuit-ui:2.13.4] C:\Users\<USER>\.gradle\caches\transforms-4\222f4099276c0321d91628bf12146474\transformed\circuit-ui-2.13.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup.designlib:circuit-ui:2.13.4] C:\Users\<USER>\.gradle\caches\transforms-4\222f4099276c0321d91628bf12146474\transformed\circuit-ui-2.13.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\56f1fdbf647eaff96ebf00a56cd72a54\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\56f1fdbf647eaff96ebf00a56cd72a54\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.airbnb.android:mavericks-hilt:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\af1026a0ab98941e7f940c3cda08bd71\transformed\mavericks-hilt-3.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:mavericks-hilt:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\af1026a0ab98941e7f940c3cda08bd71\transformed\mavericks-hilt-3.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:mavericks-compose:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\0d9a447f64c7373e913895411120ace9\transformed\mavericks-compose-3.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:mavericks-compose:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\0d9a447f64c7373e913895411120ace9\transformed\mavericks-compose-3.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:mavericks-mocking:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\a282ebe719910e4521db7acfd87744b6\transformed\mavericks-mocking-3.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:mavericks-mocking:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\a282ebe719910e4521db7acfd87744b6\transformed\mavericks-mocking-3.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:mavericks:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\a202970d2b460f0d6cb3c2f03f7cd6b0\transformed\mavericks-3.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:mavericks:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\a202970d2b460f0d6cb3c2f03f7cd6b0\transformed\mavericks-3.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.afollestad.material-dialogs:core:3.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4e2749c3ad3a5cb5c91ad98d8e64ef2f\transformed\core-3.3.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.afollestad.material-dialogs:core:3.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\4e2749c3ad3a5cb5c91ad98d8e64ef2f\transformed\core-3.3.0\AndroidManifest.xml:7:5-9:41
MERGED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:18:5-20:41
MERGED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:6:5-44
MERGED from [io.coil-kt:coil-compose:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\815c4183bcf53be1936776a0c952b550\transformed\coil-compose-2.6.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\815c4183bcf53be1936776a0c952b550\transformed\coil-compose-2.6.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\27304d73bf324a574309a7cb83a69ad6\transformed\coil-compose-base-2.6.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\27304d73bf324a574309a7cb83a69ad6\transformed\coil-compose-base-2.6.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\1142f6c10186b0585dd32ff4da1f5ce9\transformed\coil-2.6.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\1142f6c10186b0585dd32ff4da1f5ce9\transformed\coil-2.6.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8affc95c1bf0ab492f449c134f4964e7\transformed\coil-base-2.6.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8affc95c1bf0ab492f449c134f4964e7\transformed\coil-base-2.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\7ac99e26b185333d178a61f77db65d14\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\7ac99e26b185333d178a61f77db65d14\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\6bbc4a5c4ed63500292083a1ffc24782\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\6bbc4a5c4ed63500292083a1ffc24782\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup.identity.auth:auth-sdk:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f2e7f4b5c6199450632daed8f9628a8\transformed\auth-sdk-1.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup.identity.auth:auth-sdk:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f2e7f4b5c6199450632daed8f9628a8\transformed\auth-sdk-1.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\208a76efc944b3a8edf00fa399129d7c\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\208a76efc944b3a8edf00fa399129d7c\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [dev.shreyaspatil:capturable:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\add5ced8675b6385ee3a60caf6a72871\transformed\capturable-2.1.0\AndroidManifest.xml:5:5-44
MERGED from [dev.shreyaspatil:capturable:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\add5ced8675b6385ee3a60caf6a72871\transformed\capturable-2.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\32d8c721da82ee1c20f5db3a0f93b84d\transformed\webkit-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\32d8c721da82ee1c20f5db3a0f93b84d\transformed\webkit-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d352988101819e006c7e123d3310a04a\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d352988101819e006c7e123d3310a04a\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c0641230338978debab761d8c8e2b23b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c0641230338978debab761d8c8e2b23b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\51d42a11970881847f3eeebd9b1e6b8b\transformed\navigation-common-2.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\51d42a11970881847f3eeebd9b1e6b8b\transformed\navigation-common-2.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\42074f75890724836a12c77a021c7309\transformed\navigation-runtime-2.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\42074f75890724836a12c77a021c7309\transformed\navigation-runtime-2.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\624b35058fa918535e64f795c486f9b3\transformed\navigation-common-ktx-2.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\624b35058fa918535e64f795c486f9b3\transformed\navigation-common-ktx-2.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\622fb3051c19ddeb57cc108a31b47e50\transformed\navigation-compose-2.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\622fb3051c19ddeb57cc108a31b47e50\transformed\navigation-compose-2.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\ecd42d0c15ba3ec37edf07b45f590e64\transformed\navigation-runtime-ktx-2.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\ecd42d0c15ba3ec37edf07b45f590e64\transformed\navigation-runtime-ktx-2.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\561280f565be1bec57ded9bb7b820573\transformed\navigation-fragment-ktx-2.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\561280f565be1bec57ded9bb7b820573\transformed\navigation-fragment-ktx-2.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\c790a3968d30b5581f46bf141c117b9f\transformed\navigation-fragment-2.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.8.2] C:\Users\<USER>\.gradle\caches\transforms-4\c790a3968d30b5581f46bf141c117b9f\transformed\navigation-fragment-2.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2c4df0049fe6df3838f79bb92a798f00\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2c4df0049fe6df3838f79bb92a798f00\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\5fc81b5c467f15efbd01ee9e2907da29\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\5fc81b5c467f15efbd01ee9e2907da29\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e15e63b63af9fe16b8933315b533f667\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e15e63b63af9fe16b8933315b533f667\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\5b5a50e8d8f224136f03155ef0368325\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\5b5a50e8d8f224136f03155ef0368325\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\59afdd34d799c7747d219ce6ea7bb4e5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\59afdd34d799c7747d219ce6ea7bb4e5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cde5d26f4bcf9a86c2bd631a553016d5\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cde5d26f4bcf9a86c2bd631a553016d5\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.Kaaveh:sdp-compose:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab5e4eb6bbbe62ecbf705b99dd0eaf70\transformed\sdp-compose-1.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.Kaaveh:sdp-compose:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab5e4eb6bbbe62ecbf705b99dd0eaf70\transformed\sdp-compose-1.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-4\95e6a4d262e95b281c51df792e8ba726\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-4\95e6a4d262e95b281c51df792e8ba726\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-swiperefresh:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-4\3294670c912048148491d10ca25611e3\transformed\accompanist-swiperefresh-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-swiperefresh:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-4\3294670c912048148491d10ca25611e3\transformed\accompanist-swiperefresh-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bafe51bd12d3646532745d656825df70\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\bafe51bd12d3646532745d656825df70\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\06e375c8434421977ca4b1505356fc07\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\06e375c8434421977ca4b1505356fc07\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\728f960a684d825030c619518a6e897c\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\728f960a684d825030c619518a6e897c\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\a3553c94ffb9a42d7bcbbd348903785f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\a3553c94ffb9a42d7bcbbd348903785f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\340cc32149b67e7dcbdaa675121309be\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\340cc32149b67e7dcbdaa675121309be\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\a5d9350484b77cf3f3146c53b5d0ada1\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\a5d9350484b77cf3f3146c53b5d0ada1\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\9a62ad56e67be1b884ce7cee9835181b\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\9a62ad56e67be1b884ce7cee9835181b\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\0066e3b84593ce92592adad4454c96f0\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\0066e3b84593ce92592adad4454c96f0\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\c73503d83fa506d0fb42705327fe45b2\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\c73503d83fa506d0fb42705327fe45b2\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\aa8638deccfdc8890effb9d7aeceb2f4\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\aa8638deccfdc8890effb9d7aeceb2f4\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\dc6b599d38b08540c8eb079d24ba226b\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\dc6b599d38b08540c8eb079d24ba226b\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\6c73fdb2ccf16d86ef3201dbf8616f3b\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\6c73fdb2ccf16d86ef3201dbf8616f3b\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\4795aa3da7ed929ac4b15ceb6adcbbc3\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\4795aa3da7ed929ac4b15ceb6adcbbc3\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\6d58ad3ab456bc882a3f1d48b60ed688\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\6d58ad3ab456bc882a3f1d48b60ed688\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\af419de7e09ee031d89b8c3f5b39decc\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\af419de7e09ee031d89b8c3f5b39decc\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\29548e48d3d583e289c0c341f6c53f29\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\29548e48d3d583e289c0c341f6c53f29\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-work:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0b9620b9cdb8d535c49b46138a4cca\transformed\hilt-work-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.hilt:hilt-work:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f0b9620b9cdb8d535c49b46138a4cca\transformed\hilt-work-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.dagger:hilt-android:2.56.2] C:\Users\<USER>\.gradle\caches\transforms-4\0a4f64c7e32c255233ed24a394a9887f\transformed\hilt-android-2.56.2\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.56.2] C:\Users\<USER>\.gradle\caches\transforms-4\0a4f64c7e32c255233ed24a394a9887f\transformed\hilt-android-2.56.2\AndroidManifest.xml:18:3-42
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\de526a0578e170795fbc7ce4dc7819f5\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\de526a0578e170795fbc7ce4dc7819f5\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\f5f1425d1e4978261a17995de2f2e6c7\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\f5f1425d1e4978261a17995de2f2e6c7\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\682c0a3ed307cb49200ad76c1f3d1a02\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\682c0a3ed307cb49200ad76c1f3d1a02\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [com.sumup:mvp-base:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\4c82b3468554e74b56c5522122bd7d83\transformed\mvp-base-5.0.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup:mvp-base:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\4c82b3468554e74b56c5522122bd7d83\transformed\mvp-base-5.0.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.evernote:android-state:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\cb8995334c259f73a6d8a8541024248e\transformed\android-state-1.4.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.evernote:android-state:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\cb8995334c259f73a6d8a8541024248e\transformed\android-state-1.4.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\de037d029c76ad8accf275de500ba0c9\transformed\firebase-analytics-22.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\de037d029c76ad8accf275de500ba0c9\transformed\firebase-analytics-22.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\7adf4ef68603e1308d9f276765d91c42\transformed\play-services-measurement-sdk-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\7adf4ef68603e1308d9f276765d91c42\transformed\play-services-measurement-sdk-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcc3b72f95eba0d0cce8148810ef28fd\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\fcc3b72f95eba0d0cce8148810ef28fd\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6e202ec09164f6a885f9b3783eccd125\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6e202ec09164f6a885f9b3783eccd125\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\47a665339a63fda627234c653a81445e\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\47a665339a63fda627234c653a81445e\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6240e726c4ef5144f3f52d5eb6e27fe1\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6240e726c4ef5144f3f52d5eb6e27fe1\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\ad5685441e117ff27d85994c3aa70de2\transformed\play-services-measurement-base-22.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\ad5685441e117ff27d85994c3aa70de2\transformed\play-services-measurement-base-22.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b55258aaf11c7a2036372a533994273f\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b55258aaf11c7a2036372a533994273f\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\034f994bdbf2fd07ef059a1448498b08\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\034f994bdbf2fd07ef059a1448498b08\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:5:5-7:40
MERGED from [com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:5:5-7:40
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\7eb61bae09a54e29940b5eb3e36af850\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\7eb61bae09a54e29940b5eb3e36af850\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\ea75c8b7164fb774e3850e4088a40e23\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\ea75c8b7164fb774e3850e4088a40e23\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\d1b1aa02e585c9e330687a6c817500d7\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\d1b1aa02e585c9e330687a6c817500d7\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\58ccdb203ab22904b746d96ab66e8647\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\58ccdb203ab22904b746d96ab66e8647\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\be8e6d03124325b264866665fed17104\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\be8e6d03124325b264866665fed17104\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\f900e6ad2ae88254f6a07ac00b8c978b\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\f900e6ad2ae88254f6a07ac00b8c978b\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\e8a76250a913914146b7dd1a2cfbe5b6\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\e8a76250a913914146b7dd1a2cfbe5b6\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e62476b4c042791edd431f8ea302dd9\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e62476b4c042791edd431f8ea302dd9\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8ef32b97d33e76babbf431fc8353821a\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8ef32b97d33e76babbf431fc8353821a\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\df3ff725c0bedb457d599b96e7dce2e8\transformed\room-ktx-2.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\df3ff725c0bedb457d599b96e7dce2e8\transformed\room-ktx-2.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f05eaf7ef0f2c9015e659e5823b82477\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f05eaf7ef0f2c9015e659e5823b82477\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0306bc89db6006d4ec5c8eebf598197a\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0306bc89db6006d4ec5c8eebf598197a\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\41fb373b4b32a0fb5849622bc0f36577\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\41fb373b4b32a0fb5849622bc0f36577\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\f65688050329197f766babfe8912e4cb\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\f65688050329197f766babfe8912e4cb\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\096a1b28bbf75406389417c577f11255\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\096a1b28bbf75406389417c577f11255\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb50bf294701163d417e38ed66bc7049\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb50bf294701163d417e38ed66bc7049\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a935ea9f2ba4179b305800bbe11ad9e2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a935ea9f2ba4179b305800bbe11ad9e2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\d0077d2d29f12c8f8eb5f26f601033cf\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\d0077d2d29f12c8f8eb5f26f601033cf\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\252f4bab0a253411bfa4a5af35bf4301\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\252f4bab0a253411bfa4a5af35bf4301\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\777c584b16222d17d9b9a54b491e057f\transformed\lifecycle-service-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\777c584b16222d17d9b9a54b491e057f\transformed\lifecycle-service-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\33aae88c87a3d599ffb21369127bdcda\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\33aae88c87a3d599ffb21369127bdcda\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\c6823265c260db11438a1b42512fafe7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\c6823265c260db11438a1b42512fafe7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\d90e730951dba7c83dd1ff68583aab59\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\d90e730951dba7c83dd1ff68583aab59\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\ed3fddc32dad58d5f5fed01fe8b1384c\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\ed3fddc32dad58d5f5fed01fe8b1384c\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\fba83fde3eaab70ccedeecf40d34fc80\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\fba83fde3eaab70ccedeecf40d34fc80\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\66b89678b56d49beb9dd4c28762514bc\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\66b89678b56d49beb9dd4c28762514bc\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\b890659f6db25116dd25448a00dbe8e9\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\b890659f6db25116dd25448a00dbe8e9\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\824e1d4e670b8eeb1b49deaac1109b38\transformed\activity-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\824e1d4e670b8eeb1b49deaac1109b38\transformed\activity-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\251660c5ae582475e6db33f00f203beb\transformed\activity-compose-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\251660c5ae582475e6db33f00f203beb\transformed\activity-compose-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\d25e8fd67c69471399a2da8edbbfba6b\transformed\activity-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\d25e8fd67c69471399a2da8edbbfba6b\transformed\activity-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\955c7711c7167e9d93b196a9bf4006fc\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\955c7711c7167e9d93b196a9bf4006fc\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\914acba902d7170e7c627b3e54c32c13\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\914acba902d7170e7c627b3e54c32c13\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3e3f9b8282e213922209b79308a66875\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3e3f9b8282e213922209b79308a66875\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bff0aab0a70f73322b2817fb523103ec\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bff0aab0a70f73322b2817fb523103ec\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\57407fb10e0cea29c758140c4fd27bcf\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\57407fb10e0cea29c758140c4fd27bcf\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ded531c7bbfa1d22dd903befcfd57dc7\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ded531c7bbfa1d22dd903befcfd57dc7\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\9846d17c2cc1b944e646718462860232\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\9846d17c2cc1b944e646718462860232\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac23b210ee0bffdcc05003d93de6f576\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac23b210ee0bffdcc05003d93de6f576\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\7e2a3544b05530d413740856296e4693\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\7e2a3544b05530d413740856296e4693\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.kittinunf.fuel:fuel-android:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\348a6e29e36da3f011773176cceb4cad\transformed\fuel-android-2.1.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.kittinunf.fuel:fuel-android:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\348a6e29e36da3f011773176cceb4cad\transformed\fuel-android-2.1.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\172774615e637f789a4acaaba37e4492\transformed\timber-5.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\172774615e637f789a4acaaba37e4492\transformed\timber-5.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:21:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\2fc19c94460304c62776bba88cc9ef3d\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\2fc19c94460304c62776bba88cc9ef3d\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\85608ab3fe37796046b1e9073d518576\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\85608ab3fe37796046b1e9073d518576\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.sqlite:sqlite-android:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\a007e3c3ca0389380a15a448c90e1fab\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\a007e3c3ca0389380a15a448c90e1fab\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ef0870a61f83553e7109b7d080e61337\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ef0870a61f83553e7109b7d080e61337\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\9bc6b8814ebcb771e44a4775ac7f3cc3\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\9bc6b8814ebcb771e44a4775ac7f3cc3\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\a796013ccb4df496d75abd5756240bf6\transformed\viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\a796013ccb4df496d75abd5756240bf6\transformed\viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a86dcca990bb293108a9c8bee7355bf7\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a86dcca990bb293108a9c8bee7355bf7\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5011e8de47b10838518eebd977c31e8a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5011e8de47b10838518eebd977c31e8a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [com.sumup:stub:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\e0814801a626399b803268b7728505d1\transformed\stub-5.0.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup:stub:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\e0814801a626399b803268b7728505d1\transformed\stub-5.0.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup:contract:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\62f8d1bc710e35614c86a3fa8a4bb71e\transformed\contract-5.0.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup:contract:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\62f8d1bc710e35614c86a3fa8a4bb71e\transformed\contract-5.0.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\829dd5b517eefafa5a4e21d216fcf925\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\829dd5b517eefafa5a4e21d216fcf925\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\05eb7925ef580759c0e09a53846d9c5b\transformed\transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\05eb7925ef580759c0e09a53846d9c5b\transformed\transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1ddf191b25600d353b0d7f4d95e4b65c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1ddf191b25600d353b0d7f4d95e4b65c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\039e2ba24c1340d178a7afc5c69bfb7e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\039e2ba24c1340d178a7afc5c69bfb7e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5a03c8148dda9d2dd4527bd6d753fe4f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5a03c8148dda9d2dd4527bd6d753fe4f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f17619d1742aa543018e2fd7b7bdc690\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f17619d1742aa543018e2fd7b7bdc690\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bbe84aa922352f985d8a62c3c84ac18a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bbe84aa922352f985d8a62c3c84ac18a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\159a5f5f98e3bb7a6c1c7628c3d607d4\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\159a5f5f98e3bb7a6c1c7628c3d607d4\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c36ce1576d1dd2308b47daab5ede721\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c36ce1576d1dd2308b47daab5ede721\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\91e4375c2d178908431c4fb5be28d0f7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\91e4375c2d178908431c4fb5be28d0f7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-4\27e2abdd3eba7239490b45a12f269d49\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-4\27e2abdd3eba7239490b45a12f269d49\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.sunmi:printerlibrary:1.0.24] C:\Users\<USER>\.gradle\caches\transforms-4\173b165c816c25298f6e35dd0e9b44f6\transformed\printerlibrary-1.0.24\AndroidManifest.xml:5:5-7:41
MERGED from [com.sunmi:printerlibrary:1.0.24] C:\Users\<USER>\.gradle\caches\transforms-4\173b165c816c25298f6e35dd0e9b44f6\transformed\printerlibrary-1.0.24\AndroidManifest.xml:5:5-7:41
MERGED from [com.sunmi:printerx:1.0.18] C:\Users\<USER>\.gradle\caches\transforms-4\f23f023e1ff1eb4fc746e096e9d5c330\transformed\printerx-1.0.18\AndroidManifest.xml:5:5-7:41
MERGED from [com.sunmi:printerx:1.0.18] C:\Users\<USER>\.gradle\caches\transforms-4\f23f023e1ff1eb4fc746e096e9d5c330\transformed\printerx-1.0.18\AndroidManifest.xml:5:5-7:41
MERGED from [com.intuit.sdp:sdp-android:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d6dabfb88d99960db32b859140a585ce\transformed\sdp-android-1.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.intuit.sdp:sdp-android:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d6dabfb88d99960db32b859140a585ce\transformed\sdp-android-1.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.dagger:dagger-lint-aar:2.56.2] C:\Users\<USER>\.gradle\caches\transforms-4\59415d251cc0cb57b8c631f2b6d6bae2\transformed\dagger-lint-aar-2.56.2\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.56.2] C:\Users\<USER>\.gradle\caches\transforms-4\59415d251cc0cb57b8c631f2b6d6bae2\transformed\dagger-lint-aar-2.56.2\AndroidManifest.xml:18:3-42
MERGED from [com.sumup.android:logging:2.2.4] C:\Users\<USER>\.gradle\caches\transforms-4\c94a969af31a811d38b7871102ab72af\transformed\logging-2.2.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup.android:logging:2.2.4] C:\Users\<USER>\.gradle\caches\transforms-4\c94a969af31a811d38b7871102ab72af\transformed\logging-2.2.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3a571746a853aab84afaefd7c9e44e9\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f3a571746a853aab84afaefd7c9e44e9\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.sumup.analyticskit:core:0.0.19] C:\Users\<USER>\.gradle\caches\transforms-4\41caceea033f3e46deb5690152d3516e\transformed\core-0.0.19\AndroidManifest.xml:5:5-7:41
MERGED from [com.sumup.analyticskit:core:0.0.19] C:\Users\<USER>\.gradle\caches\transforms-4\41caceea033f3e46deb5690152d3516e\transformed\core-0.0.19\AndroidManifest.xml:5:5-7:41
MERGED from [org.greenrobot:eventbus:3.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\6941a24d1091c51714d1fd6238532062\transformed\eventbus-3.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [org.greenrobot:eventbus:3.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\6941a24d1091c51714d1fd6238532062\transformed\eventbus-3.3.1\AndroidManifest.xml:5:5-7:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml
uses-permission#android.permission.VIBRATE
ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:7:5-66
	android:name
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:7:22-63
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:8:5-78
	android:name
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:8:22-75
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:9:5-77
	android:name
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:9:22-74
activity#com.pluto.ui.selector.SelectorActivity
ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:12:9-16:58
	android:launchMode
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:15:13-44
	android:exported
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:14:13-37
	android:theme
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:16:13-55
	android:name
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:13:13-66
activity#com.pluto.ui.container.PlutoActivity
ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:17:9-22:75
	android:launchMode
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:20:13-44
	android:windowSoftInputMode
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:22:13-72
	android:exported
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:19:13-37
	android:theme
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:21:13-55
	android:name
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:18:13-64
activity#com.pluto.tool.modules.ruler.RulerActivity
ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:23:9-27:58
	android:launchMode
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:26:13-44
	android:exported
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:25:13-37
	android:theme
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:27:13-55
	android:name
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:24:13-70
provider#com.pluto.core.PlutoFileProvider
ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:29:9-37:20
	android:grantUriPermissions
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:33:13-47
	android:authorities
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:30:13-60
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:34:13-36:71
	android:resource
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:36:17-68
	android:name
		ADDED from [com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:35:17-67
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:12:5-79
	android:name
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:12:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:13:5-81
	android:name
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:13:22-78
uses-feature#0x00020000
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:29:5-31:35
	android:glEsVersion
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:30:9-41
	android:required
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:31:9-32
uses-feature#android.hardware.location.gps
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:32:5-34:36
	android:required
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:34:9-33
	android:name
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:33:9-53
uses-feature#android.hardware.location.network
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:35:5-37:36
	android:required
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:37:9-33
	android:name
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:36:9-57
uses-feature#android.hardware.touchscreen
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:39:5-41:35
	android:required
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:41:9-32
	android:name
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:40:9-52
uses-feature#android.hardware.screen.portrait
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:42:5-44:35
	android:required
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:44:9-32
	android:name
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:43:9-56
uses-feature#android.hardware.location
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:46:5-48:35
	android:required
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:48:9-32
	android:name
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:47:9-49
uses-feature#android.hardware.bluetooth
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:50:5-52:35
	android:required
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:52:9-32
	android:name
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:51:9-50
activity#com.sumup.merchant.reader.identitylib.ui.activities.LoginActivity
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:55:9-63:20
	android:screenOrientation
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:60:13-47
	android:launchMode
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:59:13-43
	android:windowSoftInputMode
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:62:13-55
	android:exported
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:58:13-37
	android:configChanges
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:57:13-74
	android:theme
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:61:13-64
	android:name
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:56:13-93
activity#com.sumup.merchant.reader.identitylib.ui.activities.ssologin.SSOLoginActivity
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:64:9-72:20
	android:screenOrientation
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:69:13-47
	android:launchMode
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:68:13-43
	android:windowSoftInputMode
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:71:13-55
	android:exported
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:67:13-37
	android:configChanges
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:66:13-74
	android:theme
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:70:13-64
	android:name
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:65:13-105
activity#com.sumup.merchant.reader.ui.activities.CardReaderPaymentAPIDrivenPageActivity
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:73:9-78:57
	android:screenOrientation
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:76:13-47
	android:windowSoftInputMode
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:78:13-54
	android:configChanges
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:75:13-74
	android:theme
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:77:13-58
	android:name
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:74:13-106
activity#com.sumup.merchant.reader.ui.activities.PaymentSettingsActivity
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:79:9-83:49
	android:screenOrientation
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:82:13-47
	android:label
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:81:13-70
	android:theme
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:83:13-46
	android:name
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:80:13-91
activity#com.sumup.merchant.reader.ui.activities.CardReaderSetupActivity
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:84:9-88:67
	android:screenOrientation
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:87:13-47
	android:configChanges
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:86:13-74
	android:theme
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:88:13-64
	android:name
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:85:13-91
activity#com.sumup.merchant.reader.troubleshooting.ui.BtTroubleshootingActivity
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:89:9-93:67
	android:screenOrientation
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:92:13-47
	android:configChanges
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:91:13-74
	android:theme
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:93:13-64
	android:name
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:90:13-98
activity#com.sumup.merchant.reader.troubleshooting.ReaderTroubleshootingActivity
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:94:9-97:61
	android:screenOrientation
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:96:13-47
	android:theme
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:97:13-58
	android:name
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:95:13-99
activity#com.sumup.merchant.reader.webview.ReaderWebViewActivity
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:98:9-101:61
	android:screenOrientation
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:100:13-47
	android:theme
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:101:13-58
	android:name
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:99:13-83
activity#com.sumup.merchant.reader.autoreceipt.AutoReceiptSettingsActivity
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:102:9-107:57
	android:screenOrientation
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:105:13-47
	android:windowSoftInputMode
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:107:13-54
	android:configChanges
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:104:13-74
	android:theme
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:106:13-64
	android:name
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:103:13-93
activity#com.sumup.merchant.reader.ui.activities.CardReaderPageActivity
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:108:9-112:67
	android:screenOrientation
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:111:13-47
	android:label
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:110:13-70
	android:theme
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:112:13-64
	android:name
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:109:13-90
receiver#com.sumup.merchant.reader.receiver.ShareReceiptReceiver
ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:114:9-116:40
	android:exported
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:116:13-37
	android:name
		ADDED from [com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:115:13-83
activity#com.airbnb.mvrx.launcher.MavericksLauncherActivity
ADDED from [com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:10:9-12:39
	android:exported
		ADDED from [com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:12:13-36
	android:name
		ADDED from [com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:11:13-78
activity#com.airbnb.mvrx.launcher.MavericksLauncherMockActivity
ADDED from [com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:13:9-91
	android:name
		ADDED from [com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:13:19-88
activity#com.airbnb.mvrx.launcher.MavericksLauncherTestMocksActivity
ADDED from [com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:14:9-96
	android:name
		ADDED from [com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:14:19-93
activity#net.openid.appauth.AuthorizationManagementActivity
ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:35:9-40:77
	android:launchMode
		ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:39:13-44
	android:exported
		ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:37:13-115
	android:theme
		ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:40:13-74
	android:name
		ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:36:13-78
activity#net.openid.appauth.RedirectUriReceiverActivity
ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:41:9-52:20
	android:exported
		ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:43:13-36
	android:name
		ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:42:13-74
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:${appAuthRedirectScheme}
ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:44:13-51:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:com.dasadirect.dasapos2
ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:44:13-51:29
category#android.intent.category.DEFAULT
ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:47:17-76
	android:name
		ADDED from [net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:47:27-73
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:11:9-20:19
MERGED from [com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:11:9-15:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:11:9-15:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:24:9-30:19
	android:exported
		ADDED from [com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:13:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:12:13-84
meta-data#com.google.firebase.components:com.google.firebase.perf.FirebasePerfKtxRegistrar
ADDED from [com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:14:13-16:85
	android:value
		ADDED from [com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:16:17-82
	android:name
		ADDED from [com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:15:17-112
meta-data#com.google.firebase.components:com.google.firebase.perf.FirebasePerfRegistrar
ADDED from [com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:18:17-109
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
MERGED from [com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:15:5-81
MERGED from [com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:15:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
meta-data#com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar
ADDED from [com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:30:17-128
meta-data#com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar
ADDED from [com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:33:17-117
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:16:17-126
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:19:17-115
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6e202ec09164f6a885f9b3783eccd125\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6e202ec09164f6a885f9b3783eccd125\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\47a665339a63fda627234c653a81445e\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\47a665339a63fda627234c653a81445e\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6240e726c4ef5144f3f52d5eb6e27fe1\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6240e726c4ef5144f3f52d5eb6e27fe1\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6240e726c4ef5144f3f52d5eb6e27fe1\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6240e726c4ef5144f3f52d5eb6e27fe1\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6240e726c4ef5144f3f52d5eb6e27fe1\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6240e726c4ef5144f3f52d5eb6e27fe1\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:27:22-79
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:38:17-139
service#com.google.firebase.sessions.SessionLifecycleService
ADDED from [com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:22:9-25:40
	android:enabled
		ADDED from [com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:24:13-35
	android:exported
		ADDED from [com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:23:13-80
meta-data#com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar
ADDED from [com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:30:17-117
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6e202ec09164f6a885f9b3783eccd125\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\6e202ec09164f6a885f9b3783eccd125\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:40:13-87
meta-data#com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar
ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:12:5-76
	android:name
		ADDED from [com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:12:22-73
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\c6823265c260db11438a1b42512fafe7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\c6823265c260db11438a1b42512fafe7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\transforms-4\c6823265c260db11438a1b42512fafe7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.thedasagroup.pos.handheld.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.thedasagroup.pos.handheld.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:25:13-74
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:27:13-29:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:29:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:28:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
package#woyou.aidlservice.jiuiv5
ADDED from [com.sunmi:printerx:1.0.18] C:\Users\<USER>\.gradle\caches\transforms-4\f23f023e1ff1eb4fc746e096e9d5c330\transformed\printerx-1.0.18\AndroidManifest.xml:10:9-60
	android:name
		ADDED from [com.sunmi:printerx:1.0.18] C:\Users\<USER>\.gradle\caches\transforms-4\f23f023e1ff1eb4fc746e096e9d5c330\transformed\printerx-1.0.18\AndroidManifest.xml:10:18-57
