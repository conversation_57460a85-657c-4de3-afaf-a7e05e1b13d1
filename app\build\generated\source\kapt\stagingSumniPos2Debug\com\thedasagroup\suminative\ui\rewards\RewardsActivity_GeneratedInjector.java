package com.thedasagroup.suminative.ui.rewards;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = RewardsActivity.class
)
@GeneratedEntryPoint
@InstallIn(ActivityComponent.class)
public interface RewardsActivity_GeneratedInjector {
  void injectRewardsActivity(RewardsActivity rewardsActivity);
}
