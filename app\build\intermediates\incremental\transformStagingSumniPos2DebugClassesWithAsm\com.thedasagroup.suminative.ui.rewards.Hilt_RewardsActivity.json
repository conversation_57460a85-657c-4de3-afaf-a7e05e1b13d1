{"className": "com.thedasagroup.suminative.ui.rewards.Hilt_RewardsActivity", "classAnnotations": [], "interfaces": ["android.content.ComponentCallbacks", "android.content.ComponentCallbacks2", "android.view.KeyEvent$Callback", "android.view.LayoutInflater$Factory", "android.view.LayoutInflater$Factory2", "android.view.View$OnCreateContextMenuListener", "android.view.Window$Callback", "androidx.activity.FullyDrawnReporterOwner", "androidx.activity.OnBackPressedDispatcherOwner", "androidx.activity.contextaware.ContextAware", "androidx.activity.result.ActivityResultCaller", "androidx.activity.result.ActivityResultRegistryOwner", "androidx.core.app.OnMultiWindowModeChangedProvider", "androidx.core.app.OnNewIntentProvider", "androidx.core.app.OnPictureInPictureModeChangedProvider", "androidx.core.app.OnUserLeaveHintProvider", "androidx.core.content.OnConfigurationChangedProvider", "androidx.core.content.OnTrimMemoryProvider", "androidx.core.view.KeyEventDispatcher$Component", "androidx.core.view.MenuHost", "androidx.lifecycle.HasDefaultViewModelProviderFactory", "androidx.lifecycle.LifecycleOwner", "androidx.lifecycle.ViewModelStoreOwner", "androidx.savedstate.SavedStateRegistryOwner", "dagger.hilt.internal.GeneratedComponentManager", "dagger.hilt.internal.GeneratedComponentManagerHolder"], "superClasses": ["androidx.activity.ComponentActivity", "androidx.core.app.ComponentActivity", "android.app.Activity", "android.view.ContextThemeWrapper", "android.content.ContextWrapper", "android.content.Context", "java.lang.Object"]}