package com.thedasagroup.suminative.ui.rewards;

@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\u0007\u0018\u0000 \u001e2\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0002\u001d\u001eB+\b\u0007\u0012\b\b\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0004\b\n\u0010\u000bJ\"\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0011H\u0086@\u00a2\u0006\u0002\u0010\u0013JB\u0010\u0014\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0015\u001a\u00020\u00112\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u0018H\u0086@\u00a2\u0006\u0002\u0010\u0019J\u0006\u0010\u001a\u001a\u00020\u000fJ\u0006\u0010\u001b\u001a\u00020\u000fJ\u0006\u0010\u001c\u001a\u00020\u000fR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u0006\u001f"}, d2 = {"Lcom/thedasagroup/suminative/ui/rewards/RewardsViewModel;", "Lcom/airbnb/mvrx/MavericksViewModel;", "Lcom/thedasagroup/suminative/ui/rewards/RewardsState;", "state", "getAllCustomersUseCase", "Lcom/thedasagroup/suminative/domain/rewards/GetAllCustomersUseCase;", "addPointsUseCase", "Lcom/thedasagroup/suminative/domain/rewards/AddPointsUseCase;", "prefs", "Lcom/thedasagroup/suminative/data/prefs/Prefs;", "<init>", "(Lcom/thedasagroup/suminative/ui/rewards/RewardsState;Lcom/thedasagroup/suminative/domain/rewards/GetAllCustomersUseCase;Lcom/thedasagroup/suminative/domain/rewards/AddPointsUseCase;Lcom/thedasagroup/suminative/data/prefs/Prefs;)V", "getPrefs", "()Lcom/thedasagroup/suminative/data/prefs/Prefs;", "getAllCustomers", "", "customerId", "", "businessId", "(ILjava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addPoints", "points", "orderId", "description", "", "(IILjava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearAddPointsResponse", "clearGetAllCustomersResponse", "resetState", "Factory", "Companion", "app_stagingSumniPos2Debug"})
public final class RewardsViewModel extends com.airbnb.mvrx.MavericksViewModel<com.thedasagroup.suminative.ui.rewards.RewardsState> {
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.domain.rewards.GetAllCustomersUseCase getAllCustomersUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.domain.rewards.AddPointsUseCase addPointsUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.prefs.Prefs prefs = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.ui.rewards.RewardsViewModel.Companion Companion = null;
    
    @dagger.assisted.AssistedInject()
    public RewardsViewModel(@dagger.assisted.Assisted()
    @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.rewards.RewardsState state, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.rewards.GetAllCustomersUseCase getAllCustomersUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.domain.rewards.AddPointsUseCase addPointsUseCase, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.prefs.Prefs prefs) {
        super(null, null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.prefs.Prefs getPrefs() {
        return null;
    }
    
    /**
     * Get all customers for a specific customer ID and business ID
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAllCustomers(int customerId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer businessId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Add points to a customer's rewards account
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object addPoints(int customerId, int points, @org.jetbrains.annotations.Nullable()
    java.lang.Integer businessId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer orderId, @org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Clear the add points response state
     */
    public final void clearAddPointsResponse() {
    }
    
    /**
     * Clear the get all customers response state
     */
    public final void clearGetAllCustomersResponse() {
    }
    
    /**
     * Reset all state to initial values
     */
    public final void resetState() {
    }
    
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u001b\u0010\u0006\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0003H\u0096\u0001J\u0013\u0010\n\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0007\u001a\u00020\bH\u0096\u0001\u00a8\u0006\u000b"}, d2 = {"Lcom/thedasagroup/suminative/ui/rewards/RewardsViewModel$Companion;", "Lcom/airbnb/mvrx/MavericksViewModelFactory;", "Lcom/thedasagroup/suminative/ui/rewards/RewardsViewModel;", "Lcom/thedasagroup/suminative/ui/rewards/RewardsState;", "<init>", "()V", "create", "viewModelContext", "Lcom/airbnb/mvrx/ViewModelContext;", "state", "initialState", "app_stagingSumniPos2Debug"})
    public static final class Companion implements com.airbnb.mvrx.MavericksViewModelFactory<com.thedasagroup.suminative.ui.rewards.RewardsViewModel, com.thedasagroup.suminative.ui.rewards.RewardsState> {
        
        private Companion() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.rewards.RewardsViewModel create(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.rewards.RewardsState state) {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.Nullable()
        public com.thedasagroup.suminative.ui.rewards.RewardsState initialState(@org.jetbrains.annotations.NotNull()
        com.airbnb.mvrx.ViewModelContext viewModelContext) {
            return null;
        }
    }
    
    @dagger.assisted.AssistedFactory()
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\bg\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001J\u0010\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0003H&\u00a8\u0006\u0006"}, d2 = {"Lcom/thedasagroup/suminative/ui/rewards/RewardsViewModel$Factory;", "Lcom/airbnb/mvrx/hilt/AssistedViewModelFactory;", "Lcom/thedasagroup/suminative/ui/rewards/RewardsViewModel;", "Lcom/thedasagroup/suminative/ui/rewards/RewardsState;", "create", "state", "app_stagingSumniPos2Debug"})
    public static abstract interface Factory extends com.airbnb.mvrx.hilt.AssistedViewModelFactory<com.thedasagroup.suminative.ui.rewards.RewardsViewModel, com.thedasagroup.suminative.ui.rewards.RewardsState> {
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public abstract com.thedasagroup.suminative.ui.rewards.RewardsViewModel create(@org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.ui.rewards.RewardsState state);
    }
}