package com.thedasagroup.suminative.data.model.response.reservations;

/**
 * Data class for parsing tableDetailsJson
 * Example: {"shape":"RECTANGLE","color":"#cc2e5d","position":{"row":1,"col":1},"totalRows":8,"totalColumns":8}
 */
@kotlinx.serialization.Serializable()
@kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0018\n\u0002\u0010\u000b\n\u0002\b\u0006\b\u0087\b\u0018\u0000 &2\u00020\u0001:\u0002%&B9\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\b\u00a2\u0006\u0004\b\n\u0010\u000bJ\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\bH\u00c6\u0003J\t\u0010\u001e\u001a\u00020\bH\u00c6\u0003J;\u0010\u001f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\bH\u00c6\u0001J\u0013\u0010 \u001a\u00020!2\b\u0010\"\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010#\u001a\u00020\bH\u00d6\u0001J\t\u0010$\u001a\u00020\u0003H\u00d6\u0001R\u001c\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\f\u0010\r\u001a\u0004\b\u000e\u0010\u000fR\u001c\u0010\u0004\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0010\u0010\r\u001a\u0004\b\u0011\u0010\u000fR\u001c\u0010\u0005\u001a\u00020\u00068\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0012\u0010\r\u001a\u0004\b\u0013\u0010\u0014R\u001c\u0010\u0007\u001a\u00020\b8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0015\u0010\r\u001a\u0004\b\u0016\u0010\u0017R\u001c\u0010\t\u001a\u00020\b8\u0006X\u0087\u0004\u00a2\u0006\u000e\n\u0000\u0012\u0004\b\u0018\u0010\r\u001a\u0004\b\u0019\u0010\u0017\u00a8\u0006\'"}, d2 = {"Lcom/thedasagroup/suminative/data/model/response/reservations/TableDetails;", "", "shape", "", "color", "position", "Lcom/thedasagroup/suminative/data/model/response/reservations/TablePosition;", "totalRows", "", "totalColumns", "<init>", "(Ljava/lang/String;Ljava/lang/String;Lcom/thedasagroup/suminative/data/model/response/reservations/TablePosition;II)V", "getShape$annotations", "()V", "getShape", "()Ljava/lang/String;", "getColor$annotations", "getColor", "getPosition$annotations", "getPosition", "()Lcom/thedasagroup/suminative/data/model/response/reservations/TablePosition;", "getTotalRows$annotations", "getTotalRows", "()I", "getTotalColumns$annotations", "getTotalColumns", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "toString", "$serializer", "Companion", "app_stagingSumniPos2Debug"})
public final class TableDetails {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String shape = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String color = null;
    @org.jetbrains.annotations.NotNull()
    private final com.thedasagroup.suminative.data.model.response.reservations.TablePosition position = null;
    private final int totalRows = 0;
    private final int totalColumns = 0;
    @org.jetbrains.annotations.NotNull()
    public static final com.thedasagroup.suminative.data.model.response.reservations.TableDetails.Companion Companion = null;
    
    public TableDetails(@org.jetbrains.annotations.NotNull()
    java.lang.String shape, @org.jetbrains.annotations.NotNull()
    java.lang.String color, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.reservations.TablePosition position, int totalRows, int totalColumns) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getShape() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "shape")
    @java.lang.Deprecated()
    public static void getShape$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getColor() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "color")
    @java.lang.Deprecated()
    public static void getColor$annotations() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.model.response.reservations.TablePosition getPosition() {
        return null;
    }
    
    @kotlinx.serialization.SerialName(value = "position")
    @java.lang.Deprecated()
    public static void getPosition$annotations() {
    }
    
    public final int getTotalRows() {
        return 0;
    }
    
    @kotlinx.serialization.SerialName(value = "totalRows")
    @java.lang.Deprecated()
    public static void getTotalRows$annotations() {
    }
    
    public final int getTotalColumns() {
        return 0;
    }
    
    @kotlinx.serialization.SerialName(value = "totalColumns")
    @java.lang.Deprecated()
    public static void getTotalColumns$annotations() {
    }
    
    public TableDetails() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.model.response.reservations.TablePosition component3() {
        return null;
    }
    
    public final int component4() {
        return 0;
    }
    
    public final int component5() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.thedasagroup.suminative.data.model.response.reservations.TableDetails copy(@org.jetbrains.annotations.NotNull()
    java.lang.String shape, @org.jetbrains.annotations.NotNull()
    java.lang.String color, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.reservations.TablePosition position, int totalRows, int totalColumns) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    /**
     * Data class for parsing tableDetailsJson
     * Example: {"shape":"RECTANGLE","color":"#cc2e5d","position":{"row":1,"col":1},"totalRows":8,"totalColumns":8}
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0003\u0010\u0004J\u0015\u0010\u0005\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00070\u0006\u00a2\u0006\u0002\u0010\bJ\u000e\u0010\t\u001a\u00020\u00022\u0006\u0010\n\u001a\u00020\u000bJ\u0016\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0002R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014\u00a8\u0006\u0015"}, d2 = {"com/thedasagroup/suminative/data/model/response/reservations/TableDetails.$serializer", "Lkotlinx/serialization/internal/GeneratedSerializer;", "Lcom/thedasagroup/suminative/data/model/response/reservations/TableDetails;", "<init>", "()V", "childSerializers", "", "Lkotlinx/serialization/KSerializer;", "()[Lkotlinx/serialization/KSerializer;", "deserialize", "decoder", "Lkotlinx/serialization/encoding/Decoder;", "serialize", "", "encoder", "Lkotlinx/serialization/encoding/Encoder;", "value", "descriptor", "Lkotlinx/serialization/descriptors/SerialDescriptor;", "getDescriptor", "()Lkotlinx/serialization/descriptors/SerialDescriptor;", "app_stagingSumniPos2Debug"})
    @java.lang.Deprecated()
    public static final class $serializer implements kotlinx.serialization.internal.GeneratedSerializer<com.thedasagroup.suminative.data.model.response.reservations.TableDetails> {
        @org.jetbrains.annotations.NotNull()
        public static final com.thedasagroup.suminative.data.model.response.reservations.TableDetails.$serializer INSTANCE = null;
        @org.jetbrains.annotations.NotNull()
        private static final kotlinx.serialization.descriptors.SerialDescriptor descriptor = null;
        
        /**
         * Data class for parsing tableDetailsJson
         * Example: {"shape":"RECTANGLE","color":"#cc2e5d","position":{"row":1,"col":1},"totalRows":8,"totalColumns":8}
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<?>[] childSerializers() {
            return null;
        }
        
        /**
         * Data class for parsing tableDetailsJson
         * Example: {"shape":"RECTANGLE","color":"#cc2e5d","position":{"row":1,"col":1},"totalRows":8,"totalColumns":8}
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final com.thedasagroup.suminative.data.model.response.reservations.TableDetails deserialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Decoder decoder) {
            return null;
        }
        
        /**
         * Data class for parsing tableDetailsJson
         * Example: {"shape":"RECTANGLE","color":"#cc2e5d","position":{"row":1,"col":1},"totalRows":8,"totalColumns":8}
         */
        @java.lang.Override()
        public final void serialize(@org.jetbrains.annotations.NotNull()
        kotlinx.serialization.encoding.Encoder encoder, @org.jetbrains.annotations.NotNull()
        com.thedasagroup.suminative.data.model.response.reservations.TableDetails value) {
        }
        
        private $serializer() {
            super();
        }
        
        /**
         * Data class for parsing tableDetailsJson
         * Example: {"shape":"RECTANGLE","color":"#cc2e5d","position":{"row":1,"col":1},"totalRows":8,"totalColumns":8}
         */
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.descriptors.SerialDescriptor getDescriptor() {
            return null;
        }
    }
    
    /**
     * Data class for parsing tableDetailsJson
     * Example: {"shape":"RECTANGLE","color":"#cc2e5d","position":{"row":1,"col":1},"totalRows":8,"totalColumns":8}
     */
    @kotlin.Metadata(mv = {2, 1, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a8\u0006\u0007"}, d2 = {"Lcom/thedasagroup/suminative/data/model/response/reservations/TableDetails$Companion;", "", "<init>", "()V", "serializer", "Lkotlinx/serialization/KSerializer;", "Lcom/thedasagroup/suminative/data/model/response/reservations/TableDetails;", "app_stagingSumniPos2Debug"})
    public static final class Companion {
        
        /**
         * Data class for parsing tableDetailsJson
         * Example: {"shape":"RECTANGLE","color":"#cc2e5d","position":{"row":1,"col":1},"totalRows":8,"totalColumns":8}
         */
        @org.jetbrains.annotations.NotNull()
        public final kotlinx.serialization.KSerializer<com.thedasagroup.suminative.data.model.response.reservations.TableDetails> serializer() {
            return null;
        }
        
        private Companion() {
            super();
        }
    }
}