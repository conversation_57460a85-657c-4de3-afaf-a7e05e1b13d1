package com.thedasagroup.suminative.ui.rewards;

@kotlin.Metadata(mv = {2, 1, 0}, k = 2, xi = 48, d1 = {"\u0000\u001a\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a&\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u0007H\u0007\u00a8\u0006\b"}, d2 = {"AddPointsDialog", "", "customer", "Lcom/thedasagroup/suminative/data/model/response/rewards/RewardsCustomer;", "viewModel", "Lcom/thedasagroup/suminative/ui/rewards/RewardsViewModel;", "onDismiss", "Lkotlin/Function0;", "app_stagingSumniMobilePOSDebug"})
public final class AddPointsDialogKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AddPointsDialog(@org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer customer, @org.jetbrains.annotations.NotNull()
    com.thedasagroup.suminative.ui.rewards.RewardsViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
}