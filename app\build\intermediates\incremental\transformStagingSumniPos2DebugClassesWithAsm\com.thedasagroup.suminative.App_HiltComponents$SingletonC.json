{"className": "com.thedasagroup.suminative.App_HiltComponents$SingletonC", "classAnnotations": ["dagger.Component", "javax.inject.Singleton"], "interfaces": ["com.airbnb.mvrx.hilt.CreateMavericksViewModelComponent", "com.thedasagroup.suminative.App_GeneratedInjector", "dagger.hilt.android.flags.FragmentGetContextFix$FragmentGetContextFixEntryPoint", "dagger.hilt.android.internal.managers.ActivityRetainedComponentManager$ActivityRetainedComponentBuilderEntryPoint", "dagger.hilt.android.internal.managers.HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint", "dagger.hilt.android.internal.managers.ServiceComponentManager$ServiceComponentBuilderEntryPoint", "dagger.hilt.components.SingletonComponent", "dagger.hilt.internal.GeneratedComponent"], "superClasses": ["java.lang.Object"]}