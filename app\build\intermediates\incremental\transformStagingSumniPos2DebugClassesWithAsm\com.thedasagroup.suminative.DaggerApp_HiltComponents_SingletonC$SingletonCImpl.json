{"className": "com.thedasagroup.suminative.DaggerApp_HiltComponents_SingletonC$SingletonCImpl", "classAnnotations": [], "interfaces": ["com.airbnb.mvrx.hilt.CreateMavericksViewModelComponent", "com.thedasagroup.suminative.App_GeneratedInjector", "dagger.hilt.android.flags.FragmentGetContextFix$FragmentGetContextFixEntryPoint", "dagger.hilt.android.internal.managers.ActivityRetainedComponentManager$ActivityRetainedComponentBuilderEntryPoint", "dagger.hilt.android.internal.managers.HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint", "dagger.hilt.android.internal.managers.ServiceComponentManager$ServiceComponentBuilderEntryPoint", "dagger.hilt.components.SingletonComponent", "dagger.hilt.internal.GeneratedComponent"], "superClasses": ["com.thedasagroup.suminative.App_HiltComponents$SingletonC", "java.lang.Object"]}