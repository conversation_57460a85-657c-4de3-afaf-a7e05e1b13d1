1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.dasadirect.dasapos2"
4    android:versionCode="79"
5    android:versionName="2.22" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
11-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:5:5-76
11-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:5:22-74
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:6:5-66
12-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:7:5-68
13-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:7:22-65
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:8:5-79
14-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:8:22-76
15    <!-- <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> -->
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
16-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:10:5-88
16-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:10:22-86
17    <uses-permission
17-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:11:5-107
18        android:name="android.permission.READ_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:11:22-77
19        android:maxSdkVersion="32" />
19-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:11:78-104
20    <uses-permission
20-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:12:5-108
21        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
21-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:12:22-78
22        android:maxSdkVersion="32" />
22-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:12:79-105
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
23-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:13:5-76
23-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:13:22-73
24    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
24-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:14:5-75
24-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:14:22-72
25    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
25-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:15:5-75
25-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:15:22-72
26
27    <queries>
27-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:41:5-47:15
28        <package android:name="woyou.aidlservice.jiuiv5" />
28-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:18:9-59
28-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:18:18-57
29
30        <intent>
30-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:42:9-46:18
31            <action android:name="android.intent.action.VIEW" />
31-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:13-65
31-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:21-62
32
33            <category android:name="android.intent.category.BROWSABLE" />
33-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:13-74
33-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:23-71
34
35            <data android:scheme="https" />
35-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:13-44
35-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:19-41
36        </intent>
37    </queries>
38    <queries>
38-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:41:5-47:15
39        <intent>
39-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:42:9-46:18
40            <action android:name="android.intent.action.VIEW" />
40-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:13-65
40-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:21-62
41
42            <category android:name="android.intent.category.BROWSABLE" />
42-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:13-74
42-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:23-71
43
44            <data android:scheme="https" />
44-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:13-44
44-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:19-41
45        </intent>
46    </queries>
47
48    <!--
49    <uses-permission android:name="com.google.android.gms.permission.AD_ID" tools:node="remove"/>
50    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
51    <uses-permission android:name="android.permission.BLUETOOTH" />
52    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
53    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
54    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
55    -->
56
57
58    <!-- SumUp SDK Permissions -->
59    <uses-permission
59-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:18:5-68
60        android:name="android.permission.BLUETOOTH"
60-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:18:22-65
61        android:maxSdkVersion="30" />
61-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:16:9-35
62    <uses-permission
62-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:19:5-74
63        android:name="android.permission.BLUETOOTH_ADMIN"
63-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:19:22-71
64        android:maxSdkVersion="30" />
64-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:19:9-35
65    <uses-permission
65-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:20:5-76
66        android:name="android.permission.BLUETOOTH_CONNECT"
66-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:20:22-73
67        android:usesPermissionFlags="neverForLocation" />
67-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:27:9-55
68    <uses-permission
68-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:21:5-73
69        android:name="android.permission.BLUETOOTH_SCAN"
69-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:21:22-70
70        android:usesPermissionFlags="neverForLocation" />
70-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:24:9-55
71    <uses-permission android:name="android.permission.VIBRATE" />
71-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:7:5-66
71-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:7:22-63
72    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
72-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:8:5-78
72-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:8:22-75
73    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
73-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:9:5-77
73-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:9:22-74
74    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
74-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:12:5-79
74-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:12:22-76
75    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
75-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:13:5-81
75-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:13:22-78
76
77    <uses-feature
77-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:29:5-31:35
78        android:glEsVersion="0x00020000"
78-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:30:9-41
79        android:required="true" />
79-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:31:9-32
80    <uses-feature
80-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:32:5-34:36
81        android:name="android.hardware.location.gps"
81-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:33:9-53
82        android:required="false" />
82-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:34:9-33
83    <uses-feature
83-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:35:5-37:36
84        android:name="android.hardware.location.network"
84-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:36:9-57
85        android:required="false" /> <!-- Also implied, but also really needed -->
85-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:37:9-33
86    <uses-feature
86-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:39:5-41:35
87        android:name="android.hardware.touchscreen"
87-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:40:9-52
88        android:required="true" />
88-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:41:9-32
89    <uses-feature
89-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:42:5-44:35
90        android:name="android.hardware.screen.portrait"
90-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:43:9-56
91        android:required="true" /> <!-- any location is good enough for us -->
91-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:44:9-32
92    <uses-feature
92-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:46:5-48:35
93        android:name="android.hardware.location"
93-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:47:9-49
94        android:required="true" /> <!-- Only necessary because of missing checks. See: APPS-801 -->
94-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:48:9-32
95    <uses-feature
95-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:50:5-52:35
96        android:name="android.hardware.bluetooth"
96-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:51:9-50
97        android:required="true" />
97-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:52:9-32
98
99    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
99-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
99-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
100    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
100-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:25:5-79
100-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:25:22-76
101    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
101-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:26:5-88
101-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:26:22-85
102    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
102-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:27:5-82
102-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:27:22-79
103    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
103-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:26:5-110
103-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:26:22-107
104    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
104-->[com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:12:5-76
104-->[com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:12:22-73
105
106    <permission
106-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
107        android:name="com.dasadirect.dasapos2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
107-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
108        android:protectionLevel="signature" />
108-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
109
110    <uses-permission android:name="com.dasadirect.dasapos2.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
110-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
110-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
111
112    <application
112-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:5-39:19
113        android:name="com.thedasagroup.suminative.App"
113-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:345-364
114        android:allowBackup="false"
114-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:18-45
115        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
115-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
116        android:dataExtractionRules="@xml/data_extraction_rules"
116-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:46-102
117        android:debuggable="true"
118        android:extractNativeLibs="false"
119        android:fullBackupContent="@xml/backup_rules"
119-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:103-148
120        android:icon="@mipmap/ic_launcher"
120-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:149-183
121        android:label="@string/app_name"
121-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:184-216
122        android:largeHeap="true"
122-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:401-425
123        android:roundIcon="@mipmap/ic_launcher"
123-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:217-256
124        android:supportsRtl="true"
124-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:257-283
125        android:testOnly="true"
126        android:theme="@style/Theme.SumiNative"
126-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:284-323
127        android:usesCleartextTraffic="true" >
127-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:365-400
128        <receiver
128-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:25:9-29:20
129            android:name="com.thedasagroup.suminative.ui.service.StartReceiver"
129-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:25:42-82
130            android:enabled="true"
130-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:25:19-41
131            android:exported="true" >
131-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:25:83-106
132            <intent-filter>
132-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:26:13-28:29
133                <action android:name="android.intent.action.BOOT_COMPLETED" />
133-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:17-78
133-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:25-76
134            </intent-filter>
135        </receiver>
136
137        <service
137-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:9-32:19
138            android:name="com.thedasagroup.suminative.ui.service.EndlessSocketService"
138-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:18-65
139            android:enabled="true"
139-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:66-88
140            android:exported="false"
140-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:89-113
141            android:foregroundServiceType="specialUse" >
141-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:114-156
142        </service>
143
144        <activity
144-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:51:9-64:20
145            android:name="com.thedasagroup.suminative.ui.stores.SelectStoreActivity"
145-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:52:13-58
146            android:exported="true"
146-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:53:13-36
147            android:label="@string/app_name"
147-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:54:13-45
148            android:screenOrientation="landscape"
148-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:56:13-50
149            android:theme="@style/Theme.SumiNative" >
149-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:55:13-52
150            <intent-filter>
150-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:59:13-63:29
151                <action android:name="android.intent.action.MAIN" />
151-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:60:17-69
151-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:60:25-66
152
153                <category android:name="android.intent.category.LAUNCHER" />
153-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:62:17-77
153-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:62:27-74
154            </intent-filter>
155        </activity>
156        <activity
156-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:66:9-69:15
157            android:name="com.thedasagroup.suminative.ui.login.LoginActivity"
157-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:66:19-57
158            android:screenOrientation="landscape" />
158-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:67:13-50
159        <activity
159-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:70:9-73:15
160            android:name="com.thedasagroup.suminative.ui.user_profile.SelectUserProfileActivity"
160-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:70:19-76
161            android:screenOrientation="landscape" />
161-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:71:13-50
162        <activity
162-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:74:9-77:15
163            android:name="com.thedasagroup.suminative.ui.MainActivity"
163-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:74:19-50
164            android:screenOrientation="landscape" />
164-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:75:13-50
165        <activity
165-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:78:9-81:15
166            android:name="com.thedasagroup.suminative.ui.tracking.TrackingActivity"
166-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:78:19-63
167            android:screenOrientation="landscape" />
167-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:79:13-50
168        <activity
168-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:82:9-85:15
169            android:name="com.thedasagroup.suminative.ui.stores.ClosedStoreActivity"
169-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:82:19-64
170            android:screenOrientation="landscape" />
170-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:83:13-50
171        <activity
171-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:86:9-89:15
172            android:name="com.thedasagroup.suminative.ui.stock.StockActivity"
172-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:86:19-57
173            android:screenOrientation="landscape" />
173-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:87:13-50
174        <activity
174-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:91:9-94:15
175            android:name="com.thedasagroup.suminative.ui.sales.SalesActivity"
175-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:91:19-57
176            android:screenOrientation="landscape" />
176-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:92:13-50
177        <activity
177-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:96:9-99:15
178            android:name="com.thedasagroup.suminative.LCDActivity"
178-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:96:19-46
179            android:screenOrientation="landscape" />
179-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:97:13-50
180        <activity
180-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:101:9-104:15
181            android:name="com.thedasagroup.suminative.ui.payment.PaymentActivity"
181-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:101:19-61
182            android:screenOrientation="landscape" />
182-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:102:13-50
183        <activity
183-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:106:9-111:15
184            android:name="com.thedasagroup.suminative.ui.payment.CashPaymentActivity"
184-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:106:19-65
185            android:launchMode="singleTop"
185-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:109:13-43
186            android:screenOrientation="landscape"
186-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:107:13-50
187            android:theme="@style/Theme.SumiNative" />
187-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:108:13-52
188        <activity
188-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:113:9-118:15
189            android:name="com.thedasagroup.suminative.ui.splitbill.SplitBillActivity"
189-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:113:19-65
190            android:launchMode="singleTop"
190-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:116:13-43
191            android:screenOrientation="landscape"
191-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:114:13-50
192            android:theme="@style/Theme.SumiNative" />
192-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:115:13-52
193        <activity
193-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:120:9-123:15
194            android:name="com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersActivity"
194-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:120:19-70
195            android:screenOrientation="landscape" />
195-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:121:13-50
196        <activity
196-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:125:9-128:15
197            android:name="com.thedasagroup.suminative.ui.refund.RefundSumUpActivity"
197-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:125:19-64
198            android:screenOrientation="landscape" />
198-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:126:13-50
199        <activity
199-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:130:9-133:15
200            android:name="com.thedasagroup.suminative.ui.local_orders.LocalOrdersActivity"
200-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:130:19-70
201            android:screenOrientation="landscape" />
201-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:131:13-50
202        <activity
202-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:135:9-138:15
203            android:name="com.thedasagroup.suminative.ui.reservations.ReservationsActivity"
203-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:135:19-71
204            android:screenOrientation="landscape" />
204-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:136:13-50
205        <activity
205-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:140:9-143:15
206            android:name="com.thedasagroup.suminative.ui.rewards.RewardsActivity"
206-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:140:19-61
207            android:screenOrientation="landscape" />
207-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:141:13-50
208        <activity
208-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:145:9-148:15
209            android:name="com.thedasagroup.suminative.ui.reservations.AreaTableSelectionActivity"
209-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:145:19-77
210            android:screenOrientation="landscape" />
210-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:146:13-50
211        <activity
211-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:150:9-153:15
212            android:name="com.thedasagroup.suminative.ui.reservations.AreaTableSelectionUsageExample"
212-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:150:19-81
213            android:screenOrientation="landscape" />
213-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:151:13-50
214        <activity
214-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:155:9-163:20
215            android:name="com.thedasagroup.suminative.ui.settings.SettingsActivity"
215-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:156:13-57
216            android:exported="false"
216-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:157:13-37
217            android:label="Settings"
217-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:158:13-37
218            android:parentActivityName="com.thedasagroup.suminative.ui.MainActivity" >
218-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:159:13-58
219            <meta-data
219-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:160:13-162:52
220                android:name="android.support.PARENT_ACTIVITY"
220-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:161:17-63
221                android:value=".ui.MainActivity" />
221-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\tabletpos\AndroidManifest.xml:162:17-49
222        </activity>
223
224        <!-- SumUp Payment Activity -->
225        <activity
225-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:37:9-134
226            android:name="com.thedasagroup.suminative.ui.payment.SumUpPaymentActivity"
226-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:37:19-66
227            android:exported="false"
227-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:37:67-91
228            android:theme="@style/Theme.SumiNative" />
228-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:37:92-131
229        <activity
229-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:12:9-16:58
230            android:name="com.pluto.ui.selector.SelectorActivity"
230-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:13:13-66
231            android:exported="false"
231-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:14:13-37
232            android:launchMode="singleTask"
232-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:15:13-44
233            android:theme="@style/PlutoTheme.Selector" />
233-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:16:13-55
234        <activity
234-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:17:9-22:75
235            android:name="com.pluto.ui.container.PlutoActivity"
235-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:18:13-64
236            android:exported="false"
236-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:19:13-37
237            android:launchMode="singleTask"
237-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:20:13-44
238            android:theme="@style/PlutoContainerTheme"
238-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:21:13-55
239            android:windowSoftInputMode="stateUnspecified|adjustResize" />
239-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:22:13-72
240        <activity
240-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:23:9-27:58
241            android:name="com.pluto.tool.modules.ruler.RulerActivity"
241-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:24:13-70
242            android:exported="false"
242-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:25:13-37
243            android:launchMode="singleTask"
243-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:26:13-44
244            android:theme="@style/PlutoContainerTheme" />
244-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:27:13-55
245
246        <provider
246-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:29:9-37:20
247            android:name="com.pluto.core.PlutoFileProvider"
247-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:30:13-60
248            android:authorities="pluto___com.dasadirect.dasapos2.provider"
248-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:31:13-68
249            android:exported="false"
249-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:32:13-37
250            android:grantUriPermissions="true" >
250-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:33:13-47
251            <meta-data
251-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:34:13-36:71
252                android:name="android.support.FILE_PROVIDER_PATHS"
252-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:35:17-67
253                android:resource="@xml/pluto___file_provider_paths" />
253-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:36:17-68
254        </provider>
255
256        <activity
256-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:55:9-63:20
257            android:name="com.sumup.merchant.reader.identitylib.ui.activities.LoginActivity"
257-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:56:13-93
258            android:configChanges="orientation|keyboardHidden|screenSize"
258-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:57:13-74
259            android:exported="false"
259-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:58:13-37
260            android:launchMode="singleTop"
260-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:59:13-43
261            android:screenOrientation="locked"
261-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:60:13-47
262            android:theme="@style/SumUpTheme.ActionBarNoShadow"
262-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:61:13-64
263            android:windowSoftInputMode="adjustResize" >
263-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:62:13-55
264        </activity>
265        <activity
265-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:64:9-72:20
266            android:name="com.sumup.merchant.reader.identitylib.ui.activities.ssologin.SSOLoginActivity"
266-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:65:13-105
267            android:configChanges="orientation|keyboardHidden|screenSize"
267-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:66:13-74
268            android:exported="false"
268-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:67:13-37
269            android:launchMode="singleTop"
269-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:68:13-43
270            android:screenOrientation="locked"
270-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:69:13-47
271            android:theme="@style/SumUpTheme.ActionBarNoShadow"
271-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:70:13-64
272            android:windowSoftInputMode="adjustResize" >
272-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:71:13-55
273        </activity>
274        <activity
274-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:73:9-78:57
275            android:name="com.sumup.merchant.reader.ui.activities.CardReaderPaymentAPIDrivenPageActivity"
275-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:74:13-106
276            android:configChanges="orientation|keyboardHidden|screenSize"
276-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:75:13-74
277            android:screenOrientation="locked"
277-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:76:13-47
278            android:theme="@style/SumUpTheme.NoActionBar"
278-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:77:13-58
279            android:windowSoftInputMode="stateHidden" />
279-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:78:13-54
280        <activity
280-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:79:9-83:49
281            android:name="com.sumup.merchant.reader.ui.activities.PaymentSettingsActivity"
281-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:80:13-91
282            android:label="@string/sumup_payment_setting_card_reader"
282-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:81:13-70
283            android:screenOrientation="locked"
283-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:82:13-47
284            android:theme="@style/SumUpTheme" />
284-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:83:13-46
285        <activity
285-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:84:9-88:67
286            android:name="com.sumup.merchant.reader.ui.activities.CardReaderSetupActivity"
286-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:85:13-91
287            android:configChanges="orientation|keyboardHidden|screenSize"
287-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:86:13-74
288            android:screenOrientation="locked"
288-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:87:13-47
289            android:theme="@style/SumUpTheme.ActionBarNoShadow" />
289-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:88:13-64
290        <activity
290-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:89:9-93:67
291            android:name="com.sumup.merchant.reader.troubleshooting.ui.BtTroubleshootingActivity"
291-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:90:13-98
292            android:configChanges="orientation|keyboardHidden|screenSize"
292-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:91:13-74
293            android:screenOrientation="locked"
293-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:92:13-47
294            android:theme="@style/SumUpTheme.ActionBarNoShadow" />
294-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:93:13-64
295        <activity
295-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:94:9-97:61
296            android:name="com.sumup.merchant.reader.troubleshooting.ReaderTroubleshootingActivity"
296-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:95:13-99
297            android:screenOrientation="locked"
297-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:96:13-47
298            android:theme="@style/SumUpTheme.NoActionBar" />
298-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:97:13-58
299        <activity
299-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:98:9-101:61
300            android:name="com.sumup.merchant.reader.webview.ReaderWebViewActivity"
300-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:99:13-83
301            android:screenOrientation="locked"
301-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:100:13-47
302            android:theme="@style/SumUpTheme.NoActionBar" />
302-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:101:13-58
303        <activity
303-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:102:9-107:57
304            android:name="com.sumup.merchant.reader.autoreceipt.AutoReceiptSettingsActivity"
304-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:103:13-93
305            android:configChanges="orientation|keyboardHidden|screenSize"
305-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:104:13-74
306            android:screenOrientation="locked"
306-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:105:13-47
307            android:theme="@style/SumUpTheme.ActionBarNoShadow"
307-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:106:13-64
308            android:windowSoftInputMode="stateHidden" />
308-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:107:13-54
309        <activity
309-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:108:9-112:67
310            android:name="com.sumup.merchant.reader.ui.activities.CardReaderPageActivity"
310-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:109:13-90
311            android:label="@string/sumup_payment_setting_card_reader"
311-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:110:13-70
312            android:screenOrientation="locked"
312-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:111:13-47
313            android:theme="@style/SumUpTheme.ActionBarNoShadow" />
313-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:112:13-64
314
315        <receiver
315-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:114:9-116:40
316            android:name="com.sumup.merchant.reader.receiver.ShareReceiptReceiver"
316-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:115:13-83
317            android:exported="false" /> <!-- This is exported so users can launch it from the command line. It should only be included in debug builds. -->
317-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:116:13-37
318        <activity
318-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:10:9-12:39
319            android:name="com.airbnb.mvrx.launcher.MavericksLauncherActivity"
319-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:11:13-78
320            android:exported="true" />
320-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:12:13-36
321        <activity android:name="com.airbnb.mvrx.launcher.MavericksLauncherMockActivity" />
321-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:13:9-91
321-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:13:19-88
322        <activity android:name="com.airbnb.mvrx.launcher.MavericksLauncherTestMocksActivity" />
322-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:14:9-96
322-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:14:19-93
323        <activity
323-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:35:9-40:77
324            android:name="net.openid.appauth.AuthorizationManagementActivity"
324-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:36:13-78
325            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation|keyboard|keyboardHidden"
325-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:37:13-115
326            android:exported="false"
326-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:38:13-37
327            android:launchMode="singleTask"
327-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:39:13-44
328            android:theme="@style/Theme.AppCompat.Translucent.NoTitleBar" />
328-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:40:13-74
329        <activity
329-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:41:9-52:20
330            android:name="net.openid.appauth.RedirectUriReceiverActivity"
330-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:42:13-74
331            android:exported="true" >
331-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:43:13-36
332            <intent-filter>
332-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:44:13-51:29
333                <action android:name="android.intent.action.VIEW" />
333-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:13-65
333-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:21-62
334
335                <category android:name="android.intent.category.DEFAULT" />
335-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:47:17-76
335-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:47:27-73
336                <category android:name="android.intent.category.BROWSABLE" />
336-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:13-74
336-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:23-71
337
338                <data android:scheme="com.dasadirect.dasapos2" />
338-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:13-44
338-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:19-41
339            </intent-filter>
340        </activity>
341
342        <service
342-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:11:9-20:19
343            android:name="com.google.firebase.components.ComponentDiscoveryService"
343-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:12:13-84
344            android:directBootAware="true"
344-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
345            android:exported="false" >
345-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:13:13-37
346            <meta-data
346-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:14:13-16:85
347                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfKtxRegistrar"
347-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:15:17-112
348                android:value="com.google.firebase.components.ComponentRegistrar" />
348-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:16:17-82
349            <meta-data
349-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:17:13-19:85
350                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfRegistrar"
350-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:18:17-109
351                android:value="com.google.firebase.components.ComponentRegistrar" />
351-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:19:17-82
352            <meta-data
352-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:29:13-31:85
353                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
353-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:30:17-128
354                android:value="com.google.firebase.components.ComponentRegistrar" />
354-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:31:17-82
355            <meta-data
355-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:32:13-34:85
356                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
356-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:33:17-117
357                android:value="com.google.firebase.components.ComponentRegistrar" />
357-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:34:17-82
358            <meta-data
358-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:15:13-17:85
359                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
359-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:16:17-126
360                android:value="com.google.firebase.components.ComponentRegistrar" />
360-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:17:17-82
361            <meta-data
361-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:18:13-20:85
362                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
362-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:19:17-115
363                android:value="com.google.firebase.components.ComponentRegistrar" />
363-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:20:17-82
364            <meta-data
364-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:37:13-39:85
365                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
365-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:38:17-139
366                android:value="com.google.firebase.components.ComponentRegistrar" />
366-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:39:17-82
367            <meta-data
367-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:29:13-31:85
368                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
368-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:30:17-117
369                android:value="com.google.firebase.components.ComponentRegistrar" />
369-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:31:17-82
370            <meta-data
370-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
371                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
371-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
372                android:value="com.google.firebase.components.ComponentRegistrar" />
372-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
373            <meta-data
373-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
374                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
374-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
375                android:value="com.google.firebase.components.ComponentRegistrar" />
375-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
376            <meta-data
376-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
377                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
377-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
378                android:value="com.google.firebase.components.ComponentRegistrar" />
378-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
379            <meta-data
379-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
380                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
380-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
381                android:value="com.google.firebase.components.ComponentRegistrar" />
381-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
382            <meta-data
382-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
383                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
383-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
384                android:value="com.google.firebase.components.ComponentRegistrar" />
384-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
385            <meta-data
385-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:27:13-29:85
386                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
386-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:28:17-115
387                android:value="com.google.firebase.components.ComponentRegistrar" />
387-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:29:17-82
388        </service>
389
390        <activity
390-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:23:9-25:39
391            android:name="androidx.activity.ComponentActivity"
391-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:24:13-63
392            android:exported="true" />
392-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:25:13-36
393        <activity
393-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
394            android:name="androidx.compose.ui.tooling.PreviewActivity"
394-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
395            android:exported="true" />
395-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
396
397        <service
397-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
398            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
398-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
399            android:directBootAware="false"
399-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
400            android:enabled="@bool/enable_system_alarm_service_default"
400-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
401            android:exported="false" />
401-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
402        <service
402-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
403            android:name="androidx.work.impl.background.systemjob.SystemJobService"
403-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
404            android:directBootAware="false"
404-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
405            android:enabled="@bool/enable_system_job_service_default"
405-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
406            android:exported="true"
406-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
407            android:permission="android.permission.BIND_JOB_SERVICE" />
407-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
408        <service
408-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
409            android:name="androidx.work.impl.foreground.SystemForegroundService"
409-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
410            android:directBootAware="false"
410-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
411            android:enabled="@bool/enable_system_foreground_service_default"
411-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
412            android:exported="false" />
412-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
413
414        <receiver
414-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
415            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
415-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
416            android:directBootAware="false"
416-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
417            android:enabled="true"
417-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
418            android:exported="false" />
418-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
419        <receiver
419-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
420            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
420-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
421            android:directBootAware="false"
421-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
422            android:enabled="false"
422-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
423            android:exported="false" >
423-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
424            <intent-filter>
424-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
425                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
425-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
425-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
426                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
426-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
426-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
427            </intent-filter>
428        </receiver>
429        <receiver
429-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
430            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
430-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
431            android:directBootAware="false"
431-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
432            android:enabled="false"
432-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
433            android:exported="false" >
433-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
434            <intent-filter>
434-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
435                <action android:name="android.intent.action.BATTERY_OKAY" />
435-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
435-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
436                <action android:name="android.intent.action.BATTERY_LOW" />
436-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
436-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
437            </intent-filter>
438        </receiver>
439        <receiver
439-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
440            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
440-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
441            android:directBootAware="false"
441-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
442            android:enabled="false"
442-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
443            android:exported="false" >
443-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
444            <intent-filter>
444-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
445                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
445-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
445-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
446                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
446-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
446-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
447            </intent-filter>
448        </receiver>
449        <receiver
449-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
450            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
450-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
451            android:directBootAware="false"
451-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
452            android:enabled="false"
452-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
453            android:exported="false" >
453-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
454            <intent-filter>
454-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
455                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
455-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
455-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
456            </intent-filter>
457        </receiver>
458        <receiver
458-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
459            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
459-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
460            android:directBootAware="false"
460-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
461            android:enabled="false"
461-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
462            android:exported="false" >
462-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
463            <intent-filter>
463-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
464                <action android:name="android.intent.action.BOOT_COMPLETED" />
464-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:17-78
464-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:25-76
465                <action android:name="android.intent.action.TIME_SET" />
465-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
465-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
466                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
466-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
466-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
467            </intent-filter>
468        </receiver>
469        <receiver
469-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
470            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
470-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
471            android:directBootAware="false"
471-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
472            android:enabled="@bool/enable_system_alarm_service_default"
472-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
473            android:exported="false" >
473-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
474            <intent-filter>
474-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
475                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
475-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
475-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
476            </intent-filter>
477        </receiver>
478        <receiver
478-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
479            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
479-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
480            android:directBootAware="false"
480-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
481            android:enabled="true"
481-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
482            android:exported="true"
482-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
483            android:permission="android.permission.DUMP" >
483-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
484            <intent-filter>
484-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
485                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
485-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
485-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
486            </intent-filter>
487        </receiver>
488
489        <property
489-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:30:9-32:61
490            android:name="android.adservices.AD_SERVICES_CONFIG"
490-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:31:13-65
491            android:resource="@xml/ga_ad_services_config" />
491-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:32:13-58
492
493        <service
493-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:22:9-25:40
494            android:name="com.google.firebase.sessions.SessionLifecycleService"
494-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:23:13-80
495            android:enabled="true"
495-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:24:13-35
496            android:exported="false" />
496-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:25:13-37
497
498        <provider
498-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
499            android:name="com.google.firebase.provider.FirebaseInitProvider"
499-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
500            android:authorities="com.dasadirect.dasapos2.firebaseinitprovider"
500-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
501            android:directBootAware="true"
501-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
502            android:exported="false"
502-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
503            android:initOrder="100" />
503-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
504
505        <receiver
505-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:29:9-33:20
506            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
506-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:30:13-85
507            android:enabled="true"
507-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:31:13-35
508            android:exported="false" >
508-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:32:13-37
509        </receiver>
510
511        <service
511-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:35:9-38:40
512            android:name="com.google.android.gms.measurement.AppMeasurementService"
512-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:36:13-84
513            android:enabled="true"
513-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:37:13-35
514            android:exported="false" />
514-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:38:13-37
515        <service
515-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:39:9-43:72
516            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
516-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:40:13-87
517            android:enabled="true"
517-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:41:13-35
518            android:exported="false"
518-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:42:13-37
519            android:permission="android.permission.BIND_JOB_SERVICE" />
519-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:43:13-69
520
521        <uses-library
521-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
522            android:name="android.ext.adservices"
522-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
523            android:required="false" />
523-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
524        <uses-library
524-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
525            android:name="androidx.window.extensions"
525-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
526            android:required="false" />
526-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
527        <uses-library
527-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
528            android:name="androidx.window.sidecar"
528-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
529            android:required="false" />
529-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
530
531        <meta-data
531-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
532            android:name="com.google.android.gms.version"
532-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
533            android:value="@integer/google_play_services_version" />
533-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
534
535        <service
535-->[androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:24:9-28:63
536            android:name="androidx.room.MultiInstanceInvalidationService"
536-->[androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:25:13-74
537            android:directBootAware="true"
537-->[androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:26:13-43
538            android:exported="false" />
538-->[androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:27:13-37
539        <service
539-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
540            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
540-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
541            android:exported="false" >
541-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
542            <meta-data
542-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
543                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
543-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
544                android:value="cct" />
544-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
545        </service>
546
547        <receiver
547-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
548            android:name="androidx.profileinstaller.ProfileInstallReceiver"
548-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
549            android:directBootAware="false"
549-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
550            android:enabled="true"
550-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
551            android:exported="true"
551-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
552            android:permission="android.permission.DUMP" >
552-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
553            <intent-filter>
553-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
554                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
554-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
554-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
555            </intent-filter>
556            <intent-filter>
556-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
557                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
557-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
557-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
558            </intent-filter>
559            <intent-filter>
559-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
560                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
560-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
560-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
561            </intent-filter>
562            <intent-filter>
562-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
563                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
563-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
563-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
564            </intent-filter>
565        </receiver>
566
567        <service
567-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
568            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
568-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
569            android:exported="false"
569-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
570            android:permission="android.permission.BIND_JOB_SERVICE" >
570-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
571        </service>
572
573        <receiver
573-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
574            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
574-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
575            android:exported="false" />
575-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
576    </application>
577
578</manifest>
