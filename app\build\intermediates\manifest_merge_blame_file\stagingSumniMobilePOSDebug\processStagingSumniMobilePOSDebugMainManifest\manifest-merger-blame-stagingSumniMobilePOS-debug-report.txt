1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.thedasagroup.pos.handheld"
4    android:versionCode="79"
5    android:versionName="2.22" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
11-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:5:5-76
11-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:5:22-74
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:6:5-66
12-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:7:5-68
13-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:7:22-65
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:8:5-79
14-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:8:22-76
15    <!-- <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> -->
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
16-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:10:5-88
16-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:10:22-86
17    <uses-permission
17-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:11:5-107
18        android:name="android.permission.READ_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:11:22-77
19        android:maxSdkVersion="32" />
19-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:11:78-104
20    <uses-permission
20-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:12:5-108
21        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
21-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:12:22-78
22        android:maxSdkVersion="32" />
22-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:12:79-105
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
23-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:13:5-76
23-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:13:22-73
24    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
24-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:14:5-75
24-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:14:22-72
25    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
25-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:15:5-75
25-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:15:22-72
26
27    <queries>
27-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:41:5-47:15
28        <package android:name="com.sunmi.scanner" />
28-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:18:9-53
28-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:18:18-50
29
30        <intent>
30-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:42:9-46:18
31            <action android:name="android.intent.action.VIEW" />
31-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:13-65
31-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:21-62
32
33            <category android:name="android.intent.category.BROWSABLE" />
33-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:13-74
33-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:23-71
34
35            <data android:scheme="https" />
35-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:13-44
35-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:19-41
36        </intent>
37
38        <package android:name="woyou.aidlservice.jiuiv5" />
38-->[com.sunmi:printerx:1.0.18] C:\Users\<USER>\.gradle\caches\transforms-4\f23f023e1ff1eb4fc746e096e9d5c330\transformed\printerx-1.0.18\AndroidManifest.xml:10:9-60
38-->[com.sunmi:printerx:1.0.18] C:\Users\<USER>\.gradle\caches\transforms-4\f23f023e1ff1eb4fc746e096e9d5c330\transformed\printerx-1.0.18\AndroidManifest.xml:10:18-57
39    </queries>
40    <queries>
40-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:41:5-47:15
41        <intent>
41-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:42:9-46:18
42            <action android:name="android.intent.action.VIEW" />
42-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:13-65
42-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:21-62
43
44            <category android:name="android.intent.category.BROWSABLE" />
44-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:13-74
44-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:23-71
45
46            <data android:scheme="https" />
46-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:13-44
46-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:19-41
47        </intent>
48    </queries>
49
50    <!--
51    <uses-permission android:name="com.google.android.gms.permission.AD_ID" tools:node="remove"/>
52    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
53    <uses-permission android:name="android.permission.BLUETOOTH" />
54    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
55    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
56    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
57    -->
58
59
60    <!-- SumUp SDK Permissions -->
61    <uses-permission
61-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:18:5-68
62        android:name="android.permission.BLUETOOTH"
62-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:18:22-65
63        android:maxSdkVersion="30" />
63-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:16:9-35
64    <uses-permission
64-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:19:5-74
65        android:name="android.permission.BLUETOOTH_ADMIN"
65-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:19:22-71
66        android:maxSdkVersion="30" />
66-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:19:9-35
67    <uses-permission
67-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:20:5-76
68        android:name="android.permission.BLUETOOTH_CONNECT"
68-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:20:22-73
69        android:usesPermissionFlags="neverForLocation" />
69-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:27:9-55
70    <uses-permission
70-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:21:5-73
71        android:name="android.permission.BLUETOOTH_SCAN"
71-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:21:22-70
72        android:usesPermissionFlags="neverForLocation" />
72-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:24:9-55
73    <uses-permission android:name="android.permission.VIBRATE" />
73-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:7:5-66
73-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:7:22-63
74    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
74-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:8:5-78
74-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:8:22-75
75    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
75-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:9:5-77
75-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:9:22-74
76    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
76-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:12:5-79
76-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:12:22-76
77    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
77-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:13:5-81
77-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:13:22-78
78
79    <uses-feature
79-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:29:5-31:35
80        android:glEsVersion="0x00020000"
80-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:30:9-41
81        android:required="true" />
81-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:31:9-32
82    <uses-feature
82-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:32:5-34:36
83        android:name="android.hardware.location.gps"
83-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:33:9-53
84        android:required="false" />
84-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:34:9-33
85    <uses-feature
85-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:35:5-37:36
86        android:name="android.hardware.location.network"
86-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:36:9-57
87        android:required="false" /> <!-- Also implied, but also really needed -->
87-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:37:9-33
88    <uses-feature
88-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:39:5-41:35
89        android:name="android.hardware.touchscreen"
89-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:40:9-52
90        android:required="true" />
90-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:41:9-32
91    <uses-feature
91-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:42:5-44:35
92        android:name="android.hardware.screen.portrait"
92-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:43:9-56
93        android:required="true" /> <!-- any location is good enough for us -->
93-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:44:9-32
94    <uses-feature
94-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:46:5-48:35
95        android:name="android.hardware.location"
95-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:47:9-49
96        android:required="true" /> <!-- Only necessary because of missing checks. See: APPS-801 -->
96-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:48:9-32
97    <uses-feature
97-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:50:5-52:35
98        android:name="android.hardware.bluetooth"
98-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:51:9-50
99        android:required="true" />
99-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:52:9-32
100
101    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
101-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
101-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
102    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
102-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:25:5-79
102-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:25:22-76
103    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
103-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:26:5-88
103-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:26:22-85
104    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
104-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:27:5-82
104-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:27:22-79
105    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
105-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:26:5-110
105-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:26:22-107
106    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
106-->[com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:12:5-76
106-->[com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:12:22-73
107
108    <permission
108-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
109        android:name="com.thedasagroup.pos.handheld.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
109-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
110        android:protectionLevel="signature" />
110-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
111
112    <uses-permission android:name="com.thedasagroup.pos.handheld.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
112-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
112-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
113
114    <application
114-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:5-39:19
115        android:name="com.thedasagroup.suminative.App"
115-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:345-364
116        android:allowBackup="false"
116-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:18-45
117        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
117-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
118        android:dataExtractionRules="@xml/data_extraction_rules"
118-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:46-102
119        android:debuggable="true"
120        android:extractNativeLibs="false"
121        android:fullBackupContent="@xml/backup_rules"
121-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:103-148
122        android:icon="@mipmap/ic_launcher"
122-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:149-183
123        android:label="@string/app_name"
123-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:184-216
124        android:largeHeap="true"
124-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:401-425
125        android:roundIcon="@mipmap/ic_launcher"
125-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:217-256
126        android:supportsRtl="true"
126-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:257-283
127        android:testOnly="true"
128        android:theme="@style/Theme.SumiNative"
128-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:284-323
129        android:usesCleartextTraffic="true" >
129-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:365-400
130        <receiver
130-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:25:9-29:20
131            android:name="com.thedasagroup.suminative.ui.service.StartReceiver"
131-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:25:42-82
132            android:enabled="true"
132-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:25:19-41
133            android:exported="true" >
133-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:25:83-106
134            <intent-filter>
134-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:26:13-28:29
135                <action android:name="android.intent.action.BOOT_COMPLETED" />
135-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:17-78
135-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:25-76
136            </intent-filter>
137        </receiver>
138
139        <service
139-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:9-32:19
140            android:name="com.thedasagroup.suminative.ui.service.EndlessSocketService"
140-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:18-65
141            android:enabled="true"
141-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:66-88
142            android:exported="false"
142-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:89-113
143            android:foregroundServiceType="specialUse" >
143-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:114-156
144        </service>
145
146        <activity
146-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:51:9-64:20
147            android:name="com.thedasagroup.suminative.ui.stores.SelectStoreActivity"
147-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:52:13-58
148            android:exported="true"
148-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:53:13-36
149            android:label="@string/app_name"
149-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:54:13-45
150            android:screenOrientation="portrait"
150-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:56:13-49
151            android:theme="@style/Theme.SumiNative" >
151-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:55:13-52
152            <intent-filter>
152-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:59:13-63:29
153                <action android:name="android.intent.action.MAIN" />
153-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:60:17-69
153-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:60:25-66
154
155                <category android:name="android.intent.category.LAUNCHER" />
155-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:62:17-77
155-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:62:27-74
156            </intent-filter>
157        </activity>
158        <activity
158-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:66:9-69:15
159            android:name="com.thedasagroup.suminative.ui.login.LoginActivity"
159-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:66:19-57
160            android:screenOrientation="portrait" />
160-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:67:13-49
161        <activity
161-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:70:9-73:15
162            android:name="com.thedasagroup.suminative.ui.user_profile.SelectUserProfileActivity"
162-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:70:19-76
163            android:screenOrientation="portrait" />
163-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:71:13-49
164        <activity
164-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:74:9-77:15
165            android:name="com.thedasagroup.suminative.ui.MainActivity"
165-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:74:19-50
166            android:screenOrientation="portrait" />
166-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:75:13-49
167        <activity
167-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:78:9-81:15
168            android:name="com.thedasagroup.suminative.ui.tracking.TrackingActivity"
168-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:78:19-63
169            android:screenOrientation="portrait" />
169-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:79:13-49
170        <activity
170-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:82:9-85:15
171            android:name="com.thedasagroup.suminative.ui.stores.ClosedStoreActivity"
171-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:82:19-64
172            android:screenOrientation="portrait" />
172-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:83:13-49
173        <activity
173-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:86:9-89:15
174            android:name="com.thedasagroup.suminative.ui.stock.StockActivity"
174-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:86:19-57
175            android:screenOrientation="portrait" />
175-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:87:13-49
176        <activity
176-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:91:9-94:15
177            android:name="com.thedasagroup.suminative.ui.sales.SalesActivity"
177-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:91:19-57
178            android:screenOrientation="portrait" />
178-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:92:13-49
179        <activity
179-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:96:9-99:15
180            android:name="com.thedasagroup.suminative.ui.payment.PaymentActivity"
180-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:96:19-61
181            android:screenOrientation="portrait" />
181-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:97:13-49
182        <activity
182-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:101:9-104:15
183            android:name="com.thedasagroup.suminative.ui.payment.CashPaymentActivity"
183-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:101:19-65
184            android:screenOrientation="portrait" />
184-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:102:13-49
185        <activity
185-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:106:9-109:15
186            android:name="com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersActivity"
186-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:106:19-70
187            android:screenOrientation="portrait" />
187-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:107:13-49
188        <activity
188-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:111:9-114:15
189            android:name="com.thedasagroup.suminative.ui.refund.RefundSumUpActivity"
189-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:111:19-64
190            android:screenOrientation="portrait" />
190-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:112:13-49
191        <activity
191-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:116:9-119:15
192            android:name="com.thedasagroup.suminative.ui.categories.CategoriesActivity"
192-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:116:19-67
193            android:screenOrientation="portrait" />
193-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:117:13-49
194        <activity
194-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:121:9-124:15
195            android:name="com.thedasagroup.suminative.ui.rewards.RewardsActivity"
195-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:121:19-61
196            android:screenOrientation="portrait" />
196-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:122:13-49
197        <activity
197-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:126:9-135:20
198            android:name="com.thedasagroup.suminative.ui.settings.SettingsActivity"
198-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:127:13-57
199            android:exported="false"
199-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:128:13-37
200            android:label="Settings"
200-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:129:13-37
201            android:parentActivityName="com.thedasagroup.suminative.ui.MainActivity"
201-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:131:13-58
202            android:screenOrientation="portrait" >
202-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:130:13-49
203            <meta-data
203-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:132:13-134:52
204                android:name="android.support.PARENT_ACTIVITY"
204-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:133:17-63
205                android:value=".ui.MainActivity" />
205-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:134:17-49
206        </activity>
207        <activity
207-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:137:9-139:45
208            android:name="com.thedasagroup.suminative.ui.stores.DownloadProductsActivity"
208-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:137:19-69
209            android:screenOrientation="portrait" />
209-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:138:13-49
210        <activity
210-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:141:9-143:45
211            android:name="com.thedasagroup.suminative.ui.local_orders.LocalOrdersActivity"
211-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:141:19-70
212            android:screenOrientation="portrait" />
212-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:142:13-49
213        <activity
213-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:145:9-148:15
214            android:name="com.thedasagroup.suminative.ui.reservations.AreaTableSelectionActivity"
214-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:145:19-77
215            android:screenOrientation="portrait" />
215-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:146:13-49
216
217        <!-- SumUp Payment Activity -->
218        <activity
218-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:37:9-134
219            android:name="com.thedasagroup.suminative.ui.payment.SumUpPaymentActivity"
219-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:37:19-66
220            android:exported="false"
220-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:37:67-91
221            android:theme="@style/Theme.SumiNative" />
221-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:37:92-131
222        <activity
222-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:12:9-16:58
223            android:name="com.pluto.ui.selector.SelectorActivity"
223-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:13:13-66
224            android:exported="false"
224-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:14:13-37
225            android:launchMode="singleTask"
225-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:15:13-44
226            android:theme="@style/PlutoTheme.Selector" />
226-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:16:13-55
227        <activity
227-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:17:9-22:75
228            android:name="com.pluto.ui.container.PlutoActivity"
228-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:18:13-64
229            android:exported="false"
229-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:19:13-37
230            android:launchMode="singleTask"
230-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:20:13-44
231            android:theme="@style/PlutoContainerTheme"
231-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:21:13-55
232            android:windowSoftInputMode="stateUnspecified|adjustResize" />
232-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:22:13-72
233        <activity
233-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:23:9-27:58
234            android:name="com.pluto.tool.modules.ruler.RulerActivity"
234-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:24:13-70
235            android:exported="false"
235-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:25:13-37
236            android:launchMode="singleTask"
236-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:26:13-44
237            android:theme="@style/PlutoContainerTheme" />
237-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:27:13-55
238
239        <provider
239-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:29:9-37:20
240            android:name="com.pluto.core.PlutoFileProvider"
240-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:30:13-60
241            android:authorities="pluto___com.thedasagroup.pos.handheld.provider"
241-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:31:13-68
242            android:exported="false"
242-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:32:13-37
243            android:grantUriPermissions="true" >
243-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:33:13-47
244            <meta-data
244-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:34:13-36:71
245                android:name="android.support.FILE_PROVIDER_PATHS"
245-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:35:17-67
246                android:resource="@xml/pluto___file_provider_paths" />
246-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:36:17-68
247        </provider>
248
249        <activity
249-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:55:9-63:20
250            android:name="com.sumup.merchant.reader.identitylib.ui.activities.LoginActivity"
250-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:56:13-93
251            android:configChanges="orientation|keyboardHidden|screenSize"
251-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:57:13-74
252            android:exported="false"
252-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:58:13-37
253            android:launchMode="singleTop"
253-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:59:13-43
254            android:screenOrientation="locked"
254-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:60:13-47
255            android:theme="@style/SumUpTheme.ActionBarNoShadow"
255-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:61:13-64
256            android:windowSoftInputMode="adjustResize" >
256-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:62:13-55
257        </activity>
258        <activity
258-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:64:9-72:20
259            android:name="com.sumup.merchant.reader.identitylib.ui.activities.ssologin.SSOLoginActivity"
259-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:65:13-105
260            android:configChanges="orientation|keyboardHidden|screenSize"
260-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:66:13-74
261            android:exported="false"
261-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:67:13-37
262            android:launchMode="singleTop"
262-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:68:13-43
263            android:screenOrientation="locked"
263-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:69:13-47
264            android:theme="@style/SumUpTheme.ActionBarNoShadow"
264-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:70:13-64
265            android:windowSoftInputMode="adjustResize" >
265-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:71:13-55
266        </activity>
267        <activity
267-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:73:9-78:57
268            android:name="com.sumup.merchant.reader.ui.activities.CardReaderPaymentAPIDrivenPageActivity"
268-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:74:13-106
269            android:configChanges="orientation|keyboardHidden|screenSize"
269-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:75:13-74
270            android:screenOrientation="locked"
270-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:76:13-47
271            android:theme="@style/SumUpTheme.NoActionBar"
271-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:77:13-58
272            android:windowSoftInputMode="stateHidden" />
272-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:78:13-54
273        <activity
273-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:79:9-83:49
274            android:name="com.sumup.merchant.reader.ui.activities.PaymentSettingsActivity"
274-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:80:13-91
275            android:label="@string/sumup_payment_setting_card_reader"
275-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:81:13-70
276            android:screenOrientation="locked"
276-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:82:13-47
277            android:theme="@style/SumUpTheme" />
277-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:83:13-46
278        <activity
278-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:84:9-88:67
279            android:name="com.sumup.merchant.reader.ui.activities.CardReaderSetupActivity"
279-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:85:13-91
280            android:configChanges="orientation|keyboardHidden|screenSize"
280-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:86:13-74
281            android:screenOrientation="locked"
281-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:87:13-47
282            android:theme="@style/SumUpTheme.ActionBarNoShadow" />
282-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:88:13-64
283        <activity
283-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:89:9-93:67
284            android:name="com.sumup.merchant.reader.troubleshooting.ui.BtTroubleshootingActivity"
284-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:90:13-98
285            android:configChanges="orientation|keyboardHidden|screenSize"
285-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:91:13-74
286            android:screenOrientation="locked"
286-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:92:13-47
287            android:theme="@style/SumUpTheme.ActionBarNoShadow" />
287-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:93:13-64
288        <activity
288-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:94:9-97:61
289            android:name="com.sumup.merchant.reader.troubleshooting.ReaderTroubleshootingActivity"
289-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:95:13-99
290            android:screenOrientation="locked"
290-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:96:13-47
291            android:theme="@style/SumUpTheme.NoActionBar" />
291-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:97:13-58
292        <activity
292-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:98:9-101:61
293            android:name="com.sumup.merchant.reader.webview.ReaderWebViewActivity"
293-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:99:13-83
294            android:screenOrientation="locked"
294-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:100:13-47
295            android:theme="@style/SumUpTheme.NoActionBar" />
295-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:101:13-58
296        <activity
296-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:102:9-107:57
297            android:name="com.sumup.merchant.reader.autoreceipt.AutoReceiptSettingsActivity"
297-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:103:13-93
298            android:configChanges="orientation|keyboardHidden|screenSize"
298-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:104:13-74
299            android:screenOrientation="locked"
299-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:105:13-47
300            android:theme="@style/SumUpTheme.ActionBarNoShadow"
300-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:106:13-64
301            android:windowSoftInputMode="stateHidden" />
301-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:107:13-54
302        <activity
302-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:108:9-112:67
303            android:name="com.sumup.merchant.reader.ui.activities.CardReaderPageActivity"
303-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:109:13-90
304            android:label="@string/sumup_payment_setting_card_reader"
304-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:110:13-70
305            android:screenOrientation="locked"
305-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:111:13-47
306            android:theme="@style/SumUpTheme.ActionBarNoShadow" />
306-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:112:13-64
307
308        <receiver
308-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:114:9-116:40
309            android:name="com.sumup.merchant.reader.receiver.ShareReceiptReceiver"
309-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:115:13-83
310            android:exported="false" /> <!-- This is exported so users can launch it from the command line. It should only be included in debug builds. -->
310-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:116:13-37
311        <activity
311-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:10:9-12:39
312            android:name="com.airbnb.mvrx.launcher.MavericksLauncherActivity"
312-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:11:13-78
313            android:exported="true" />
313-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:12:13-36
314        <activity android:name="com.airbnb.mvrx.launcher.MavericksLauncherMockActivity" />
314-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:13:9-91
314-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:13:19-88
315        <activity android:name="com.airbnb.mvrx.launcher.MavericksLauncherTestMocksActivity" />
315-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:14:9-96
315-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:14:19-93
316        <activity
316-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:35:9-40:77
317            android:name="net.openid.appauth.AuthorizationManagementActivity"
317-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:36:13-78
318            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation|keyboard|keyboardHidden"
318-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:37:13-115
319            android:exported="false"
319-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:38:13-37
320            android:launchMode="singleTask"
320-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:39:13-44
321            android:theme="@style/Theme.AppCompat.Translucent.NoTitleBar" />
321-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:40:13-74
322        <activity
322-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:41:9-52:20
323            android:name="net.openid.appauth.RedirectUriReceiverActivity"
323-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:42:13-74
324            android:exported="true" >
324-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:43:13-36
325            <intent-filter>
325-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:44:13-51:29
326                <action android:name="android.intent.action.VIEW" />
326-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:13-65
326-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:21-62
327
328                <category android:name="android.intent.category.DEFAULT" />
328-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:47:17-76
328-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:47:27-73
329                <category android:name="android.intent.category.BROWSABLE" />
329-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:13-74
329-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:23-71
330
331                <data android:scheme="com.dasadirect.dasapos2" />
331-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:13-44
331-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:19-41
332            </intent-filter>
333        </activity>
334
335        <service
335-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:11:9-20:19
336            android:name="com.google.firebase.components.ComponentDiscoveryService"
336-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:12:13-84
337            android:directBootAware="true"
337-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
338            android:exported="false" >
338-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:13:13-37
339            <meta-data
339-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:14:13-16:85
340                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfKtxRegistrar"
340-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:15:17-112
341                android:value="com.google.firebase.components.ComponentRegistrar" />
341-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:16:17-82
342            <meta-data
342-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:17:13-19:85
343                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfRegistrar"
343-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:18:17-109
344                android:value="com.google.firebase.components.ComponentRegistrar" />
344-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:19:17-82
345            <meta-data
345-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:29:13-31:85
346                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
346-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:30:17-128
347                android:value="com.google.firebase.components.ComponentRegistrar" />
347-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:31:17-82
348            <meta-data
348-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:32:13-34:85
349                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
349-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:33:17-117
350                android:value="com.google.firebase.components.ComponentRegistrar" />
350-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:34:17-82
351            <meta-data
351-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:15:13-17:85
352                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
352-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:16:17-126
353                android:value="com.google.firebase.components.ComponentRegistrar" />
353-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:17:17-82
354            <meta-data
354-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:18:13-20:85
355                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
355-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:19:17-115
356                android:value="com.google.firebase.components.ComponentRegistrar" />
356-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:20:17-82
357            <meta-data
357-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:37:13-39:85
358                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
358-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:38:17-139
359                android:value="com.google.firebase.components.ComponentRegistrar" />
359-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:39:17-82
360            <meta-data
360-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:29:13-31:85
361                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
361-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:30:17-117
362                android:value="com.google.firebase.components.ComponentRegistrar" />
362-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:31:17-82
363            <meta-data
363-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
364                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
364-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
365                android:value="com.google.firebase.components.ComponentRegistrar" />
365-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
366            <meta-data
366-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
367                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
367-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
368                android:value="com.google.firebase.components.ComponentRegistrar" />
368-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
369            <meta-data
369-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
370                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
370-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
371                android:value="com.google.firebase.components.ComponentRegistrar" />
371-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
372            <meta-data
372-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
373                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
373-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
374                android:value="com.google.firebase.components.ComponentRegistrar" />
374-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
375            <meta-data
375-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
376                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
376-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
377                android:value="com.google.firebase.components.ComponentRegistrar" />
377-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
378            <meta-data
378-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:27:13-29:85
379                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
379-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:28:17-115
380                android:value="com.google.firebase.components.ComponentRegistrar" />
380-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:29:17-82
381        </service>
382
383        <activity
383-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:23:9-25:39
384            android:name="androidx.activity.ComponentActivity"
384-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:24:13-63
385            android:exported="true" />
385-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:25:13-36
386        <activity
386-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
387            android:name="androidx.compose.ui.tooling.PreviewActivity"
387-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
388            android:exported="true" />
388-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
389
390        <service
390-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
391            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
391-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
392            android:directBootAware="false"
392-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
393            android:enabled="@bool/enable_system_alarm_service_default"
393-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
394            android:exported="false" />
394-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
395        <service
395-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
396            android:name="androidx.work.impl.background.systemjob.SystemJobService"
396-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
397            android:directBootAware="false"
397-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
398            android:enabled="@bool/enable_system_job_service_default"
398-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
399            android:exported="true"
399-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
400            android:permission="android.permission.BIND_JOB_SERVICE" />
400-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
401        <service
401-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
402            android:name="androidx.work.impl.foreground.SystemForegroundService"
402-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
403            android:directBootAware="false"
403-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
404            android:enabled="@bool/enable_system_foreground_service_default"
404-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
405            android:exported="false" />
405-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
406
407        <receiver
407-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
408            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
408-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
409            android:directBootAware="false"
409-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
410            android:enabled="true"
410-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
411            android:exported="false" />
411-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
412        <receiver
412-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
413            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
413-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
414            android:directBootAware="false"
414-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
415            android:enabled="false"
415-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
416            android:exported="false" >
416-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
417            <intent-filter>
417-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
418                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
418-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
418-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
419                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
419-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
419-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
420            </intent-filter>
421        </receiver>
422        <receiver
422-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
423            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
423-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
424            android:directBootAware="false"
424-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
425            android:enabled="false"
425-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
426            android:exported="false" >
426-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
427            <intent-filter>
427-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
428                <action android:name="android.intent.action.BATTERY_OKAY" />
428-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
428-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
429                <action android:name="android.intent.action.BATTERY_LOW" />
429-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
429-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
430            </intent-filter>
431        </receiver>
432        <receiver
432-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
433            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
433-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
434            android:directBootAware="false"
434-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
435            android:enabled="false"
435-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
436            android:exported="false" >
436-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
437            <intent-filter>
437-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
438                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
438-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
438-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
439                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
439-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
439-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
440            </intent-filter>
441        </receiver>
442        <receiver
442-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
443            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
443-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
444            android:directBootAware="false"
444-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
445            android:enabled="false"
445-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
446            android:exported="false" >
446-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
447            <intent-filter>
447-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
448                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
448-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
448-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
449            </intent-filter>
450        </receiver>
451        <receiver
451-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
452            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
452-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
453            android:directBootAware="false"
453-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
454            android:enabled="false"
454-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
455            android:exported="false" >
455-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
456            <intent-filter>
456-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
457                <action android:name="android.intent.action.BOOT_COMPLETED" />
457-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:17-78
457-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:25-76
458                <action android:name="android.intent.action.TIME_SET" />
458-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
458-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
459                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
459-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
459-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
460            </intent-filter>
461        </receiver>
462        <receiver
462-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
463            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
463-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
464            android:directBootAware="false"
464-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
465            android:enabled="@bool/enable_system_alarm_service_default"
465-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
466            android:exported="false" >
466-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
467            <intent-filter>
467-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
468                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
468-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
468-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
469            </intent-filter>
470        </receiver>
471        <receiver
471-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
472            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
472-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
473            android:directBootAware="false"
473-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
474            android:enabled="true"
474-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
475            android:exported="true"
475-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
476            android:permission="android.permission.DUMP" >
476-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
477            <intent-filter>
477-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
478                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
478-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
478-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
479            </intent-filter>
480        </receiver>
481
482        <property
482-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:30:9-32:61
483            android:name="android.adservices.AD_SERVICES_CONFIG"
483-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:31:13-65
484            android:resource="@xml/ga_ad_services_config" />
484-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:32:13-58
485
486        <service
486-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:22:9-25:40
487            android:name="com.google.firebase.sessions.SessionLifecycleService"
487-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:23:13-80
488            android:enabled="true"
488-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:24:13-35
489            android:exported="false" />
489-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:25:13-37
490
491        <provider
491-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
492            android:name="com.google.firebase.provider.FirebaseInitProvider"
492-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
493            android:authorities="com.thedasagroup.pos.handheld.firebaseinitprovider"
493-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
494            android:directBootAware="true"
494-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
495            android:exported="false"
495-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
496            android:initOrder="100" />
496-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
497
498        <receiver
498-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:29:9-33:20
499            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
499-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:30:13-85
500            android:enabled="true"
500-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:31:13-35
501            android:exported="false" >
501-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:32:13-37
502        </receiver>
503
504        <service
504-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:35:9-38:40
505            android:name="com.google.android.gms.measurement.AppMeasurementService"
505-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:36:13-84
506            android:enabled="true"
506-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:37:13-35
507            android:exported="false" />
507-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:38:13-37
508        <service
508-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:39:9-43:72
509            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
509-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:40:13-87
510            android:enabled="true"
510-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:41:13-35
511            android:exported="false"
511-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:42:13-37
512            android:permission="android.permission.BIND_JOB_SERVICE" />
512-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:43:13-69
513
514        <uses-library
514-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
515            android:name="android.ext.adservices"
515-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
516            android:required="false" />
516-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
517        <uses-library
517-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
518            android:name="androidx.window.extensions"
518-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
519            android:required="false" />
519-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
520        <uses-library
520-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
521            android:name="androidx.window.sidecar"
521-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
522            android:required="false" />
522-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
523
524        <meta-data
524-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
525            android:name="com.google.android.gms.version"
525-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
526            android:value="@integer/google_play_services_version" />
526-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
527
528        <service
528-->[androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:24:9-28:63
529            android:name="androidx.room.MultiInstanceInvalidationService"
529-->[androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:25:13-74
530            android:directBootAware="true"
530-->[androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:26:13-43
531            android:exported="false" />
531-->[androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:27:13-37
532        <service
532-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
533            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
533-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
534            android:exported="false" >
534-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
535            <meta-data
535-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
536                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
536-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
537                android:value="cct" />
537-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
538        </service>
539
540        <receiver
540-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
541            android:name="androidx.profileinstaller.ProfileInstallReceiver"
541-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
542            android:directBootAware="false"
542-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
543            android:enabled="true"
543-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
544            android:exported="true"
544-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
545            android:permission="android.permission.DUMP" >
545-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
546            <intent-filter>
546-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
547                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
547-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
547-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
548            </intent-filter>
549            <intent-filter>
549-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
550                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
550-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
550-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
551            </intent-filter>
552            <intent-filter>
552-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
553                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
553-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
553-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
554            </intent-filter>
555            <intent-filter>
555-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
556                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
556-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
556-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
557            </intent-filter>
558        </receiver>
559
560        <service
560-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
561            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
561-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
562            android:exported="false"
562-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
563            android:permission="android.permission.BIND_JOB_SERVICE" >
563-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
564        </service>
565
566        <receiver
566-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
567            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
567-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
568            android:exported="false" />
568-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
569    </application>
570
571</manifest>
