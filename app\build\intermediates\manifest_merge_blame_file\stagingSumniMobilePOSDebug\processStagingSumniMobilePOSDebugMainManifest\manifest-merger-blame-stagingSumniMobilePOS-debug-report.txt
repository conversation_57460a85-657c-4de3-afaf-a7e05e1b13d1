1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.thedasagroup.pos.handheld"
4    android:versionCode="79"
5    android:versionName="2.22" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
11-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:5:5-76
11-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:5:22-74
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:6:5-66
12-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:7:5-68
13-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:7:22-65
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:8:5-79
14-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:8:22-76
15    <!-- <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> -->
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
16-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:10:5-88
16-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:10:22-86
17    <uses-permission
17-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:11:5-107
18        android:name="android.permission.READ_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:11:22-77
19        android:maxSdkVersion="32" />
19-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:11:78-104
20    <uses-permission
20-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:12:5-108
21        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
21-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:12:22-78
22        android:maxSdkVersion="32" />
22-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:12:79-105
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
23-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:13:5-76
23-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:13:22-73
24    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
24-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:14:5-75
24-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:14:22-72
25    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
25-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:15:5-75
25-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:15:22-72
26
27    <queries>
27-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:41:5-47:15
28        <package android:name="com.sunmi.scanner" />
28-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:18:9-53
28-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:18:18-50
29
30        <intent>
30-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:42:9-46:18
31            <action android:name="android.intent.action.VIEW" />
31-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:13-65
31-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:21-62
32
33            <category android:name="android.intent.category.BROWSABLE" />
33-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:13-74
33-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:23-71
34
35            <data android:scheme="https" />
35-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:13-44
35-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:19-41
36        </intent>
37
38        <package android:name="woyou.aidlservice.jiuiv5" />
38-->[com.sunmi:printerx:1.0.18] C:\Users\<USER>\.gradle\caches\transforms-4\f23f023e1ff1eb4fc746e096e9d5c330\transformed\printerx-1.0.18\AndroidManifest.xml:10:9-60
38-->[com.sunmi:printerx:1.0.18] C:\Users\<USER>\.gradle\caches\transforms-4\f23f023e1ff1eb4fc746e096e9d5c330\transformed\printerx-1.0.18\AndroidManifest.xml:10:18-57
39    </queries>
40    <queries>
40-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:41:5-47:15
41        <intent>
41-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:42:9-46:18
42            <action android:name="android.intent.action.VIEW" />
42-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:13-65
42-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:21-62
43
44            <category android:name="android.intent.category.BROWSABLE" />
44-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:13-74
44-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:23-71
45
46            <data android:scheme="https" />
46-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:13-44
46-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:19-41
47        </intent>
48    </queries>
49
50    <!--
51    <uses-permission android:name="com.google.android.gms.permission.AD_ID" tools:node="remove"/>
52    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
53    <uses-permission android:name="android.permission.BLUETOOTH" />
54    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
55    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
56    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
57    -->
58
59
60    <!-- SumUp SDK Permissions -->
61    <uses-permission
61-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:18:5-68
62        android:name="android.permission.BLUETOOTH"
62-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:18:22-65
63        android:maxSdkVersion="30" />
63-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:16:9-35
64    <uses-permission
64-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:19:5-74
65        android:name="android.permission.BLUETOOTH_ADMIN"
65-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:19:22-71
66        android:maxSdkVersion="30" />
66-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:19:9-35
67    <uses-permission
67-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:20:5-76
68        android:name="android.permission.BLUETOOTH_CONNECT"
68-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:20:22-73
69        android:usesPermissionFlags="neverForLocation" />
69-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:27:9-55
70    <uses-permission
70-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:21:5-73
71        android:name="android.permission.BLUETOOTH_SCAN"
71-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:21:22-70
72        android:usesPermissionFlags="neverForLocation" />
72-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:24:9-55
73    <uses-permission android:name="android.permission.VIBRATE" />
73-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:7:5-66
73-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:7:22-63
74    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
74-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:8:5-78
74-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:8:22-75
75    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
75-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:9:5-77
75-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:9:22-74
76    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
76-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:12:5-79
76-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:12:22-76
77    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
77-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:13:5-81
77-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:13:22-78
78
79    <uses-feature
79-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:29:5-31:35
80        android:glEsVersion="0x00020000"
80-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:30:9-41
81        android:required="true" />
81-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:31:9-32
82    <uses-feature
82-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:32:5-34:36
83        android:name="android.hardware.location.gps"
83-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:33:9-53
84        android:required="false" />
84-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:34:9-33
85    <uses-feature
85-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:35:5-37:36
86        android:name="android.hardware.location.network"
86-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:36:9-57
87        android:required="false" /> <!-- Also implied, but also really needed -->
87-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:37:9-33
88    <uses-feature
88-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:39:5-41:35
89        android:name="android.hardware.touchscreen"
89-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:40:9-52
90        android:required="true" />
90-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:41:9-32
91    <uses-feature
91-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:42:5-44:35
92        android:name="android.hardware.screen.portrait"
92-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:43:9-56
93        android:required="true" /> <!-- any location is good enough for us -->
93-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:44:9-32
94    <uses-feature
94-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:46:5-48:35
95        android:name="android.hardware.location"
95-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:47:9-49
96        android:required="true" /> <!-- Only necessary because of missing checks. See: APPS-801 -->
96-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:48:9-32
97    <uses-feature
97-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:50:5-52:35
98        android:name="android.hardware.bluetooth"
98-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:51:9-50
99        android:required="true" />
99-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:52:9-32
100
101    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
101-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
101-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
102    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
102-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:25:5-79
102-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:25:22-76
103    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
103-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:26:5-88
103-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:26:22-85
104    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
104-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:27:5-82
104-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:27:22-79
105    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
105-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:26:5-110
105-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:26:22-107
106    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
106-->[com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:12:5-76
106-->[com.github.instacart:truetime-android:4.0.0.alpha] C:\Users\<USER>\.gradle\caches\transforms-4\7ead962b14cf0e1ed7a9ff16e4d82d20\transformed\truetime-android-4.0.0.alpha\AndroidManifest.xml:12:22-73
107
108    <permission
108-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
109        android:name="com.thedasagroup.pos.handheld.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
109-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
110        android:protectionLevel="signature" />
110-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
111
112    <uses-permission android:name="com.thedasagroup.pos.handheld.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
112-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
112-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
113
114    <application
114-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:5-39:19
115        android:name="com.thedasagroup.suminative.App"
115-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:345-364
116        android:allowBackup="false"
116-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:18-45
117        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
117-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\966abd985cb3a4de813b1dc1fc749c3e\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
118        android:dataExtractionRules="@xml/data_extraction_rules"
118-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:46-102
119        android:debuggable="true"
120        android:extractNativeLibs="false"
121        android:fullBackupContent="@xml/backup_rules"
121-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:103-148
122        android:icon="@mipmap/ic_launcher"
122-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:149-183
123        android:label="@string/app_name"
123-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:184-216
124        android:largeHeap="true"
124-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:401-425
125        android:roundIcon="@mipmap/ic_launcher"
125-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:217-256
126        android:supportsRtl="true"
126-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:257-283
127        android:testOnly="true"
128        android:theme="@style/Theme.SumiNative"
128-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:284-323
129        android:usesCleartextTraffic="true" >
129-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:23:365-400
130        <receiver
130-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:25:9-29:20
131            android:name="com.thedasagroup.suminative.ui.service.StartReceiver"
131-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:25:42-82
132            android:enabled="true"
132-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:25:19-41
133            android:exported="true" >
133-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:25:83-106
134            <intent-filter>
134-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:26:13-28:29
135                <action android:name="android.intent.action.BOOT_COMPLETED" />
135-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:17-78
135-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:25-76
136            </intent-filter>
137        </receiver>
138
139        <service
139-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:9-32:19
140            android:name="com.thedasagroup.suminative.ui.service.EndlessSocketService"
140-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:18-65
141            android:enabled="true"
141-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:66-88
142            android:exported="false"
142-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:89-113
143            android:foregroundServiceType="specialUse" >
143-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:31:114-156
144        </service>
145
146        <activity
146-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:51:9-64:20
147            android:name="com.thedasagroup.suminative.ui.stores.SelectStoreActivity"
147-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:52:13-58
148            android:exported="true"
148-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:53:13-36
149            android:label="@string/app_name"
149-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:54:13-45
150            android:screenOrientation="portrait"
150-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:56:13-49
151            android:theme="@style/Theme.SumiNative" >
151-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:55:13-52
152            <intent-filter>
152-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:59:13-63:29
153                <action android:name="android.intent.action.MAIN" />
153-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:60:17-69
153-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:60:25-66
154
155                <category android:name="android.intent.category.LAUNCHER" />
155-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:62:17-77
155-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:62:27-74
156            </intent-filter>
157        </activity>
158        <activity
158-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:66:9-69:15
159            android:name="com.thedasagroup.suminative.ui.login.LoginActivity"
159-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:66:19-57
160            android:screenOrientation="portrait" />
160-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:67:13-49
161        <activity
161-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:70:9-73:15
162            android:name="com.thedasagroup.suminative.ui.user_profile.SelectUserProfileActivity"
162-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:70:19-76
163            android:screenOrientation="portrait" />
163-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:71:13-49
164        <activity
164-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:74:9-77:15
165            android:name="com.thedasagroup.suminative.ui.MainActivity"
165-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:74:19-50
166            android:screenOrientation="portrait" />
166-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:75:13-49
167        <activity
167-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:78:9-81:15
168            android:name="com.thedasagroup.suminative.ui.tracking.TrackingActivity"
168-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:78:19-63
169            android:screenOrientation="portrait" />
169-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:79:13-49
170        <activity
170-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:82:9-85:15
171            android:name="com.thedasagroup.suminative.ui.stores.ClosedStoreActivity"
171-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:82:19-64
172            android:screenOrientation="portrait" />
172-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:83:13-49
173        <activity
173-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:86:9-89:15
174            android:name="com.thedasagroup.suminative.ui.stock.StockActivity"
174-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:86:19-57
175            android:screenOrientation="portrait" />
175-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:87:13-49
176        <activity
176-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:91:9-94:15
177            android:name="com.thedasagroup.suminative.ui.sales.SalesActivity"
177-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:91:19-57
178            android:screenOrientation="portrait" />
178-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:92:13-49
179        <activity
179-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:96:9-99:15
180            android:name="com.thedasagroup.suminative.ui.payment.PaymentActivity"
180-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:96:19-61
181            android:screenOrientation="portrait" />
181-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:97:13-49
182        <activity
182-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:101:9-104:15
183            android:name="com.thedasagroup.suminative.ui.payment.CashPaymentActivity"
183-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:101:19-65
184            android:screenOrientation="portrait" />
184-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:102:13-49
185        <activity
185-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:106:9-109:15
186            android:name="com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersActivity"
186-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:106:19-70
187            android:screenOrientation="portrait" />
187-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:107:13-49
188        <activity
188-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:111:9-114:15
189            android:name="com.thedasagroup.suminative.ui.refund.RefundSumUpActivity"
189-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:111:19-64
190            android:screenOrientation="portrait" />
190-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:112:13-49
191        <activity
191-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:116:9-119:15
192            android:name="com.thedasagroup.suminative.ui.categories.CategoriesActivity"
192-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:116:19-67
193            android:screenOrientation="portrait" />
193-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:117:13-49
194        <activity
194-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:121:9-130:20
195            android:name="com.thedasagroup.suminative.ui.settings.SettingsActivity"
195-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:122:13-57
196            android:exported="false"
196-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:123:13-37
197            android:label="Settings"
197-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:124:13-37
198            android:parentActivityName="com.thedasagroup.suminative.ui.MainActivity"
198-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:126:13-58
199            android:screenOrientation="portrait" >
199-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:125:13-49
200            <meta-data
200-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:127:13-129:52
201                android:name="android.support.PARENT_ACTIVITY"
201-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:128:17-63
202                android:value=".ui.MainActivity" />
202-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:129:17-49
203        </activity>
204        <activity
204-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:132:9-134:45
205            android:name="com.thedasagroup.suminative.ui.stores.DownloadProductsActivity"
205-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:132:19-69
206            android:screenOrientation="portrait" />
206-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:133:13-49
207        <activity
207-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:136:9-138:45
208            android:name="com.thedasagroup.suminative.ui.local_orders.LocalOrdersActivity"
208-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:136:19-70
209            android:screenOrientation="portrait" />
209-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:137:13-49
210        <activity
210-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:140:9-143:15
211            android:name="com.thedasagroup.suminative.ui.reservations.AreaTableSelectionActivity"
211-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:140:19-77
212            android:screenOrientation="portrait" />
212-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\mobilepos\AndroidManifest.xml:141:13-49
213
214        <!-- SumUp Payment Activity -->
215        <activity
215-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:37:9-134
216            android:name="com.thedasagroup.suminative.ui.payment.SumUpPaymentActivity"
216-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:37:19-66
217            android:exported="false"
217-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:37:67-91
218            android:theme="@style/Theme.SumiNative" />
218-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:37:92-131
219        <activity
219-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:12:9-16:58
220            android:name="com.pluto.ui.selector.SelectorActivity"
220-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:13:13-66
221            android:exported="false"
221-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:14:13-37
222            android:launchMode="singleTask"
222-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:15:13-44
223            android:theme="@style/PlutoTheme.Selector" />
223-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:16:13-55
224        <activity
224-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:17:9-22:75
225            android:name="com.pluto.ui.container.PlutoActivity"
225-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:18:13-64
226            android:exported="false"
226-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:19:13-37
227            android:launchMode="singleTask"
227-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:20:13-44
228            android:theme="@style/PlutoContainerTheme"
228-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:21:13-55
229            android:windowSoftInputMode="stateUnspecified|adjustResize" />
229-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:22:13-72
230        <activity
230-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:23:9-27:58
231            android:name="com.pluto.tool.modules.ruler.RulerActivity"
231-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:24:13-70
232            android:exported="false"
232-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:25:13-37
233            android:launchMode="singleTask"
233-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:26:13-44
234            android:theme="@style/PlutoContainerTheme" />
234-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:27:13-55
235
236        <provider
236-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:29:9-37:20
237            android:name="com.pluto.core.PlutoFileProvider"
237-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:30:13-60
238            android:authorities="pluto___com.thedasagroup.pos.handheld.provider"
238-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:31:13-68
239            android:exported="false"
239-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:32:13-37
240            android:grantUriPermissions="true" >
240-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:33:13-47
241            <meta-data
241-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:34:13-36:71
242                android:name="android.support.FILE_PROVIDER_PATHS"
242-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:35:17-67
243                android:resource="@xml/pluto___file_provider_paths" />
243-->[com.plutolib:pluto:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7a95fa8e21201ecca244b975e86fc406\transformed\pluto-2.2.1\AndroidManifest.xml:36:17-68
244        </provider>
245
246        <activity
246-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:55:9-63:20
247            android:name="com.sumup.merchant.reader.identitylib.ui.activities.LoginActivity"
247-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:56:13-93
248            android:configChanges="orientation|keyboardHidden|screenSize"
248-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:57:13-74
249            android:exported="false"
249-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:58:13-37
250            android:launchMode="singleTop"
250-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:59:13-43
251            android:screenOrientation="locked"
251-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:60:13-47
252            android:theme="@style/SumUpTheme.ActionBarNoShadow"
252-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:61:13-64
253            android:windowSoftInputMode="adjustResize" >
253-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:62:13-55
254        </activity>
255        <activity
255-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:64:9-72:20
256            android:name="com.sumup.merchant.reader.identitylib.ui.activities.ssologin.SSOLoginActivity"
256-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:65:13-105
257            android:configChanges="orientation|keyboardHidden|screenSize"
257-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:66:13-74
258            android:exported="false"
258-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:67:13-37
259            android:launchMode="singleTop"
259-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:68:13-43
260            android:screenOrientation="locked"
260-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:69:13-47
261            android:theme="@style/SumUpTheme.ActionBarNoShadow"
261-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:70:13-64
262            android:windowSoftInputMode="adjustResize" >
262-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:71:13-55
263        </activity>
264        <activity
264-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:73:9-78:57
265            android:name="com.sumup.merchant.reader.ui.activities.CardReaderPaymentAPIDrivenPageActivity"
265-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:74:13-106
266            android:configChanges="orientation|keyboardHidden|screenSize"
266-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:75:13-74
267            android:screenOrientation="locked"
267-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:76:13-47
268            android:theme="@style/SumUpTheme.NoActionBar"
268-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:77:13-58
269            android:windowSoftInputMode="stateHidden" />
269-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:78:13-54
270        <activity
270-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:79:9-83:49
271            android:name="com.sumup.merchant.reader.ui.activities.PaymentSettingsActivity"
271-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:80:13-91
272            android:label="@string/sumup_payment_setting_card_reader"
272-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:81:13-70
273            android:screenOrientation="locked"
273-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:82:13-47
274            android:theme="@style/SumUpTheme" />
274-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:83:13-46
275        <activity
275-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:84:9-88:67
276            android:name="com.sumup.merchant.reader.ui.activities.CardReaderSetupActivity"
276-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:85:13-91
277            android:configChanges="orientation|keyboardHidden|screenSize"
277-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:86:13-74
278            android:screenOrientation="locked"
278-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:87:13-47
279            android:theme="@style/SumUpTheme.ActionBarNoShadow" />
279-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:88:13-64
280        <activity
280-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:89:9-93:67
281            android:name="com.sumup.merchant.reader.troubleshooting.ui.BtTroubleshootingActivity"
281-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:90:13-98
282            android:configChanges="orientation|keyboardHidden|screenSize"
282-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:91:13-74
283            android:screenOrientation="locked"
283-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:92:13-47
284            android:theme="@style/SumUpTheme.ActionBarNoShadow" />
284-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:93:13-64
285        <activity
285-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:94:9-97:61
286            android:name="com.sumup.merchant.reader.troubleshooting.ReaderTroubleshootingActivity"
286-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:95:13-99
287            android:screenOrientation="locked"
287-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:96:13-47
288            android:theme="@style/SumUpTheme.NoActionBar" />
288-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:97:13-58
289        <activity
289-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:98:9-101:61
290            android:name="com.sumup.merchant.reader.webview.ReaderWebViewActivity"
290-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:99:13-83
291            android:screenOrientation="locked"
291-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:100:13-47
292            android:theme="@style/SumUpTheme.NoActionBar" />
292-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:101:13-58
293        <activity
293-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:102:9-107:57
294            android:name="com.sumup.merchant.reader.autoreceipt.AutoReceiptSettingsActivity"
294-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:103:13-93
295            android:configChanges="orientation|keyboardHidden|screenSize"
295-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:104:13-74
296            android:screenOrientation="locked"
296-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:105:13-47
297            android:theme="@style/SumUpTheme.ActionBarNoShadow"
297-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:106:13-64
298            android:windowSoftInputMode="stateHidden" />
298-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:107:13-54
299        <activity
299-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:108:9-112:67
300            android:name="com.sumup.merchant.reader.ui.activities.CardReaderPageActivity"
300-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:109:13-90
301            android:label="@string/sumup_payment_setting_card_reader"
301-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:110:13-70
302            android:screenOrientation="locked"
302-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:111:13-47
303            android:theme="@style/SumUpTheme.ActionBarNoShadow" />
303-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:112:13-64
304
305        <receiver
305-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:114:9-116:40
306            android:name="com.sumup.merchant.reader.receiver.ShareReceiptReceiver"
306-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:115:13-83
307            android:exported="false" /> <!-- This is exported so users can launch it from the command line. It should only be included in debug builds. -->
307-->[com.sumup:merchant-sdk:5.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\bdcff7e5329d1bfe023fc8af73cd330f\transformed\merchant-sdk-5.0.3\AndroidManifest.xml:116:13-37
308        <activity
308-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:10:9-12:39
309            android:name="com.airbnb.mvrx.launcher.MavericksLauncherActivity"
309-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:11:13-78
310            android:exported="true" />
310-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:12:13-36
311        <activity android:name="com.airbnb.mvrx.launcher.MavericksLauncherMockActivity" />
311-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:13:9-91
311-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:13:19-88
312        <activity android:name="com.airbnb.mvrx.launcher.MavericksLauncherTestMocksActivity" />
312-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:14:9-96
312-->[com.airbnb.android:mavericks-launcher:3.0.10] C:\Users\<USER>\.gradle\caches\transforms-4\485d2ec7b131bb2061ee9cdcd7e84c5c\transformed\mavericks-launcher-3.0.10\AndroidManifest.xml:14:19-93
313        <activity
313-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:35:9-40:77
314            android:name="net.openid.appauth.AuthorizationManagementActivity"
314-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:36:13-78
315            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation|keyboard|keyboardHidden"
315-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:37:13-115
316            android:exported="false"
316-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:38:13-37
317            android:launchMode="singleTask"
317-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:39:13-44
318            android:theme="@style/Theme.AppCompat.Translucent.NoTitleBar" />
318-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:40:13-74
319        <activity
319-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:41:9-52:20
320            android:name="net.openid.appauth.RedirectUriReceiverActivity"
320-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:42:13-74
321            android:exported="true" >
321-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:43:13-36
322            <intent-filter>
322-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:44:13-51:29
323                <action android:name="android.intent.action.VIEW" />
323-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:13-65
323-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:43:21-62
324
325                <category android:name="android.intent.category.DEFAULT" />
325-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:47:17-76
325-->[net.openid:appauth:0.11.1] C:\Users\<USER>\.gradle\caches\transforms-4\5c229965b32ff2b4c6966f416b21fcec\transformed\appauth-0.11.1\AndroidManifest.xml:47:27-73
326                <category android:name="android.intent.category.BROWSABLE" />
326-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:13-74
326-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:44:23-71
327
328                <data android:scheme="com.dasadirect.dasapos2" />
328-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:13-44
328-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:45:19-41
329            </intent-filter>
330        </activity>
331
332        <service
332-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:11:9-20:19
333            android:name="com.google.firebase.components.ComponentDiscoveryService"
333-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:12:13-84
334            android:directBootAware="true"
334-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
335            android:exported="false" >
335-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:13:13-37
336            <meta-data
336-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:14:13-16:85
337                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfKtxRegistrar"
337-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:15:17-112
338                android:value="com.google.firebase.components.ComponentRegistrar" />
338-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:16:17-82
339            <meta-data
339-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:17:13-19:85
340                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfRegistrar"
340-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:18:17-109
341                android:value="com.google.firebase.components.ComponentRegistrar" />
341-->[com.google.firebase:firebase-perf:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\0b7899ecc0a5ffde96c80e6cbb863b9e\transformed\firebase-perf-21.0.1\AndroidManifest.xml:19:17-82
342            <meta-data
342-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:29:13-31:85
343                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
343-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:30:17-128
344                android:value="com.google.firebase.components.ComponentRegistrar" />
344-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:31:17-82
345            <meta-data
345-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:32:13-34:85
346                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
346-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:33:17-117
347                android:value="com.google.firebase.components.ComponentRegistrar" />
347-->[com.google.firebase:firebase-config:22.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\abcd3bf578c42c83bad7330861f20858\transformed\firebase-config-22.0.0\AndroidManifest.xml:34:17-82
348            <meta-data
348-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:15:13-17:85
349                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
349-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:16:17-126
350                android:value="com.google.firebase.components.ComponentRegistrar" />
350-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:17:17-82
351            <meta-data
351-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:18:13-20:85
352                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
352-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:19:17-115
353                android:value="com.google.firebase.components.ComponentRegistrar" />
353-->[com.google.firebase:firebase-crashlytics:19.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b9b32cd5af3f2f99937a893eafc0c473\transformed\firebase-crashlytics-19.0.2\AndroidManifest.xml:20:17-82
354            <meta-data
354-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:37:13-39:85
355                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
355-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:38:17-139
356                android:value="com.google.firebase.components.ComponentRegistrar" />
356-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:39:17-82
357            <meta-data
357-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:29:13-31:85
358                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
358-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:30:17-117
359                android:value="com.google.firebase.components.ComponentRegistrar" />
359-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:31:17-82
360            <meta-data
360-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
361                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
361-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
362                android:value="com.google.firebase.components.ComponentRegistrar" />
362-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
363            <meta-data
363-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
364                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
364-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
365                android:value="com.google.firebase.components.ComponentRegistrar" />
365-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5a7e1cc2fcca33cb5b2db548e51cc139\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
366            <meta-data
366-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
367                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
367-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
368                android:value="com.google.firebase.components.ComponentRegistrar" />
368-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f7ad57c372d36fa916d12c31dcb06f3\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
369            <meta-data
369-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
370                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
370-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
371                android:value="com.google.firebase.components.ComponentRegistrar" />
371-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
372            <meta-data
372-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
373                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
373-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
374                android:value="com.google.firebase.components.ComponentRegistrar" />
374-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\301bcd3944a431cc56b57a3e3cc83ffb\transformed\firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
375            <meta-data
375-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:27:13-29:85
376                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
376-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:28:17-115
377                android:value="com.google.firebase.components.ComponentRegistrar" />
377-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\0076b21b861de29f6ebc8d50161043c3\transformed\firebase-datatransport-18.1.8\AndroidManifest.xml:29:17-82
378        </service>
379
380        <activity
380-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:23:9-25:39
381            android:name="androidx.activity.ComponentActivity"
381-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:24:13-63
382            android:exported="true" />
382-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b0b1eef25c7bb21c9fee47a7e64d22c\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:25:13-36
383        <activity
383-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
384            android:name="androidx.compose.ui.tooling.PreviewActivity"
384-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
385            android:exported="true" />
385-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\5687ec69136a199c6adca09a6153a654\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
386
387        <service
387-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
388            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
388-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
389            android:directBootAware="false"
389-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
390            android:enabled="@bool/enable_system_alarm_service_default"
390-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
391            android:exported="false" />
391-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
392        <service
392-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
393            android:name="androidx.work.impl.background.systemjob.SystemJobService"
393-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
394            android:directBootAware="false"
394-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
395            android:enabled="@bool/enable_system_job_service_default"
395-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
396            android:exported="true"
396-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
397            android:permission="android.permission.BIND_JOB_SERVICE" />
397-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
398        <service
398-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
399            android:name="androidx.work.impl.foreground.SystemForegroundService"
399-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
400            android:directBootAware="false"
400-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
401            android:enabled="@bool/enable_system_foreground_service_default"
401-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
402            android:exported="false" />
402-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
403
404        <receiver
404-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
405            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
405-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
406            android:directBootAware="false"
406-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
407            android:enabled="true"
407-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
408            android:exported="false" />
408-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
409        <receiver
409-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
410            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
410-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
411            android:directBootAware="false"
411-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
412            android:enabled="false"
412-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
413            android:exported="false" >
413-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
414            <intent-filter>
414-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
415                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
415-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
415-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
416                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
416-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
416-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
417            </intent-filter>
418        </receiver>
419        <receiver
419-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
420            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
420-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
421            android:directBootAware="false"
421-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
422            android:enabled="false"
422-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
423            android:exported="false" >
423-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
424            <intent-filter>
424-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
425                <action android:name="android.intent.action.BATTERY_OKAY" />
425-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
425-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
426                <action android:name="android.intent.action.BATTERY_LOW" />
426-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
426-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
427            </intent-filter>
428        </receiver>
429        <receiver
429-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
430            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
430-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
431            android:directBootAware="false"
431-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
432            android:enabled="false"
432-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
433            android:exported="false" >
433-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
434            <intent-filter>
434-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
435                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
435-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
435-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
436                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
436-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
436-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
437            </intent-filter>
438        </receiver>
439        <receiver
439-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
440            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
440-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
441            android:directBootAware="false"
441-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
442            android:enabled="false"
442-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
443            android:exported="false" >
443-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
444            <intent-filter>
444-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
445                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
445-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
445-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
446            </intent-filter>
447        </receiver>
448        <receiver
448-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
449            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
449-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
450            android:directBootAware="false"
450-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
451            android:enabled="false"
451-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
452            android:exported="false" >
452-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
453            <intent-filter>
453-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
454                <action android:name="android.intent.action.BOOT_COMPLETED" />
454-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:17-78
454-->C:\Users\<USER>\AndroidStudioProjects\POS Active\Tablet POS\app\src\main\AndroidManifest.xml:27:25-76
455                <action android:name="android.intent.action.TIME_SET" />
455-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
455-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
456                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
456-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
456-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
457            </intent-filter>
458        </receiver>
459        <receiver
459-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
460            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
460-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
461            android:directBootAware="false"
461-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
462            android:enabled="@bool/enable_system_alarm_service_default"
462-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
463            android:exported="false" >
463-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
464            <intent-filter>
464-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
465                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
465-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
465-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
466            </intent-filter>
467        </receiver>
468        <receiver
468-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
469            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
469-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
470            android:directBootAware="false"
470-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
471            android:enabled="true"
471-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
472            android:exported="true"
472-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
473            android:permission="android.permission.DUMP" >
473-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
474            <intent-filter>
474-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
475                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
475-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
475-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\a1a41cc7e4dc3fc6174e200181ab82dd\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
476            </intent-filter>
477        </receiver>
478
479        <property
479-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:30:9-32:61
480            android:name="android.adservices.AD_SERVICES_CONFIG"
480-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:31:13-65
481            android:resource="@xml/ga_ad_services_config" />
481-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2ba9d82bcda8c37390eddccc2d0a5395\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:32:13-58
482
483        <service
483-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:22:9-25:40
484            android:name="com.google.firebase.sessions.SessionLifecycleService"
484-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:23:13-80
485            android:enabled="true"
485-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:24:13-35
486            android:exported="false" />
486-->[com.google.firebase:firebase-sessions:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\71db23fbc98722fd5f1c0b610ddb4f44\transformed\firebase-sessions-2.0.2\AndroidManifest.xml:25:13-37
487
488        <provider
488-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
489            android:name="com.google.firebase.provider.FirebaseInitProvider"
489-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
490            android:authorities="com.thedasagroup.pos.handheld.firebaseinitprovider"
490-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
491            android:directBootAware="true"
491-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
492            android:exported="false"
492-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
493            android:initOrder="100" />
493-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\637bd8f655eaeec966b551d0cb6191c7\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
494
495        <receiver
495-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:29:9-33:20
496            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
496-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:30:13-85
497            android:enabled="true"
497-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:31:13-35
498            android:exported="false" >
498-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:32:13-37
499        </receiver>
500
501        <service
501-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:35:9-38:40
502            android:name="com.google.android.gms.measurement.AppMeasurementService"
502-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:36:13-84
503            android:enabled="true"
503-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:37:13-35
504            android:exported="false" />
504-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:38:13-37
505        <service
505-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:39:9-43:72
506            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
506-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:40:13-87
507            android:enabled="true"
507-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:41:13-35
508            android:exported="false"
508-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:42:13-37
509            android:permission="android.permission.BIND_JOB_SERVICE" />
509-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9354644815c067f1c90c5137dc8bf616\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:43:13-69
510
511        <uses-library
511-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
512            android:name="android.ext.adservices"
512-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
513            android:required="false" />
513-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\a09f691e26ef4f438d2ee1c3966c8abd\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
514        <uses-library
514-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
515            android:name="androidx.window.extensions"
515-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
516            android:required="false" />
516-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
517        <uses-library
517-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
518            android:name="androidx.window.sidecar"
518-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
519            android:required="false" />
519-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cf97bd77db084052b4c77fe263aec54d\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
520
521        <meta-data
521-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
522            android:name="com.google.android.gms.version"
522-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
523            android:value="@integer/google_play_services_version" />
523-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ab28c4c4a3aadf9599b12b43718d1cb\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
524
525        <service
525-->[androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:24:9-28:63
526            android:name="androidx.room.MultiInstanceInvalidationService"
526-->[androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:25:13-74
527            android:directBootAware="true"
527-->[androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:26:13-43
528            android:exported="false" />
528-->[androidx.room:room-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\ca8c0287d3787630cf23d450d4d76ac2\transformed\room-runtime-2.5.1\AndroidManifest.xml:27:13-37
529        <service
529-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
530            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
530-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
531            android:exported="false" >
531-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
532            <meta-data
532-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
533                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
533-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
534                android:value="cct" />
534-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\bae51c8f9e7a82cd85bca106f23998e0\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
535        </service>
536
537        <receiver
537-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
538            android:name="androidx.profileinstaller.ProfileInstallReceiver"
538-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
539            android:directBootAware="false"
539-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
540            android:enabled="true"
540-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
541            android:exported="true"
541-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
542            android:permission="android.permission.DUMP" >
542-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
543            <intent-filter>
543-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
544                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
544-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
544-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
545            </intent-filter>
546            <intent-filter>
546-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
547                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
547-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
547-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
548            </intent-filter>
549            <intent-filter>
549-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
550                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
550-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
550-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
551            </intent-filter>
552            <intent-filter>
552-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
553                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
553-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
553-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2d1ce3b4527b0916ecd2f14808e13c95\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
554            </intent-filter>
555        </receiver>
556
557        <service
557-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
558            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
558-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
559            android:exported="false"
559-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
560            android:permission="android.permission.BIND_JOB_SERVICE" >
560-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
561        </service>
562
563        <receiver
563-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
564            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
564-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
565            android:exported="false" />
565-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-4\980bbb8dca8d6a634eda639fa0ffec74\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
566    </application>
567
568</manifest>
