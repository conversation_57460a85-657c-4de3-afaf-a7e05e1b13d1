package com.thedasagroup.suminative;

import androidx.hilt.work.HiltWrapper_WorkerFactoryModule;
import com.airbnb.mvrx.hilt.CreateMavericksViewModelComponent;
import com.airbnb.mvrx.hilt.HiltMavericksEntryPoint;
import com.airbnb.mvrx.hilt.MavericksViewModelComponent;
import com.airbnb.mvrx.hilt.MavericksViewModelComponentBuilder;
import com.airbnb.mvrx.hilt.MavericksViewModelScoped;
import com.sumup.identity.auth.implementation.di.HiltAuthRequestProviderModule;
import com.sumup.identity.auth.implementation.di.HiltWrapper_HiltAuthModule;
import com.sumup.identity.auth.implementation.di.HiltWrapper_HiltInternalAuthRequestProviderModule;
import com.thedasagroup.suminative.di.AppViewModelModule;
import com.thedasagroup.suminative.di.CategoryModule;
import com.thedasagroup.suminative.di.HiltWrapper_AppUseCaseModule;
import com.thedasagroup.suminative.di.HiltWrapper_OrderUseCaseModule;
import com.thedasagroup.suminative.di.HiltWrapper_RepoModule;
import com.thedasagroup.suminative.di.PaymentModule;
import com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersActivity_GeneratedInjector;
import com.thedasagroup.suminative.ui.payment.CashPaymentActivity_GeneratedInjector;
import com.thedasagroup.suminative.ui.payment.PaymentActivity_GeneratedInjector;
import com.thedasagroup.suminative.ui.payment.SumUpPaymentActivity_GeneratedInjector;
import com.thedasagroup.suminative.ui.refund.RefundSumUpActivity_GeneratedInjector;
import com.thedasagroup.suminative.ui.reservations.AreaTableSelectionActivity_GeneratedInjector;
import com.thedasagroup.suminative.ui.rewards.RewardsActivity_GeneratedInjector;
import com.thedasagroup.suminative.ui.service.EndlessSocketService_GeneratedInjector;
import com.thedasagroup.suminative.ui.service.MySocketJobService_GeneratedInjector;
import com.thedasagroup.suminative.ui.settings.SettingsActivity_GeneratedInjector;
import com.thedasagroup.suminative.work.SyncOrdersWorker_HiltModule;
import com.thedasagroup.suminative.work.UploadLogsWorker_HiltModule;
import dagger.Binds;
import dagger.Component;
import dagger.Module;
import dagger.Subcomponent;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.android.components.ActivityRetainedComponent;
import dagger.hilt.android.components.FragmentComponent;
import dagger.hilt.android.components.ServiceComponent;
import dagger.hilt.android.components.ViewComponent;
import dagger.hilt.android.components.ViewModelComponent;
import dagger.hilt.android.components.ViewWithFragmentComponent;
import dagger.hilt.android.flags.FragmentGetContextFix;
import dagger.hilt.android.flags.HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.HiltViewModelFactory;
import dagger.hilt.android.internal.lifecycle.HiltWrapper_DefaultViewModelFactories_ActivityModule;
import dagger.hilt.android.internal.lifecycle.HiltWrapper_HiltViewModelFactory_ActivityCreatorEntryPoint;
import dagger.hilt.android.internal.lifecycle.HiltWrapper_HiltViewModelFactory_ViewModelModule;
import dagger.hilt.android.internal.managers.ActivityComponentManager;
import dagger.hilt.android.internal.managers.FragmentComponentManager;
import dagger.hilt.android.internal.managers.HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint;
import dagger.hilt.android.internal.managers.HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedLifecycleEntryPoint;
import dagger.hilt.android.internal.managers.HiltWrapper_ActivityRetainedComponentManager_LifecycleModule;
import dagger.hilt.android.internal.managers.HiltWrapper_SavedStateHandleModule;
import dagger.hilt.android.internal.managers.ServiceComponentManager;
import dagger.hilt.android.internal.managers.ViewComponentManager;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.HiltWrapper_ActivityModule;
import dagger.hilt.android.scopes.ActivityRetainedScoped;
import dagger.hilt.android.scopes.ActivityScoped;
import dagger.hilt.android.scopes.FragmentScoped;
import dagger.hilt.android.scopes.ServiceScoped;
import dagger.hilt.android.scopes.ViewModelScoped;
import dagger.hilt.android.scopes.ViewScoped;
import dagger.hilt.components.SingletonComponent;
import dagger.hilt.internal.GeneratedComponent;
import dagger.hilt.migration.DisableInstallInCheck;
import javax.inject.Singleton;

public final class App_HiltComponents {
  private App_HiltComponents() {
  }

  @Module(
      subcomponents = ServiceC.class
  )
  @DisableInstallInCheck
  abstract interface ServiceCBuilderModule {
    @Binds
    ServiceComponentBuilder bind(ServiceC.Builder builder);
  }

  @Module(
      subcomponents = MavericksViewModelC.class
  )
  @DisableInstallInCheck
  abstract interface MavericksViewModelCBuilderModule {
    @Binds
    MavericksViewModelComponentBuilder bind(MavericksViewModelC.Builder builder);
  }

  @Module(
      subcomponents = ActivityRetainedC.class
  )
  @DisableInstallInCheck
  abstract interface ActivityRetainedCBuilderModule {
    @Binds
    ActivityRetainedComponentBuilder bind(ActivityRetainedC.Builder builder);
  }

  @Module(
      subcomponents = ActivityC.class
  )
  @DisableInstallInCheck
  abstract interface ActivityCBuilderModule {
    @Binds
    ActivityComponentBuilder bind(ActivityC.Builder builder);
  }

  @Module(
      subcomponents = ViewModelC.class
  )
  @DisableInstallInCheck
  abstract interface ViewModelCBuilderModule {
    @Binds
    ViewModelComponentBuilder bind(ViewModelC.Builder builder);
  }

  @Module(
      subcomponents = ViewC.class
  )
  @DisableInstallInCheck
  abstract interface ViewCBuilderModule {
    @Binds
    ViewComponentBuilder bind(ViewC.Builder builder);
  }

  @Module(
      subcomponents = FragmentC.class
  )
  @DisableInstallInCheck
  abstract interface FragmentCBuilderModule {
    @Binds
    FragmentComponentBuilder bind(FragmentC.Builder builder);
  }

  @Module(
      subcomponents = ViewWithFragmentC.class
  )
  @DisableInstallInCheck
  abstract interface ViewWithFragmentCBuilderModule {
    @Binds
    ViewWithFragmentComponentBuilder bind(ViewWithFragmentC.Builder builder);
  }

  @Component(
      modules = {
          ActivityRetainedCBuilderModule.class,
          MavericksViewModelCBuilderModule.class,
          ServiceCBuilderModule.class,
          ApplicationContextModule.class,
          CategoryModule.class,
          HiltAuthRequestProviderModule.class,
          HiltWrapper_AppUseCaseModule.class,
          HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule.class,
          HiltWrapper_HiltAuthModule.class,
          HiltWrapper_HiltInternalAuthRequestProviderModule.class,
          HiltWrapper_OrderUseCaseModule.class,
          HiltWrapper_RepoModule.class,
          HiltWrapper_WorkerFactoryModule.class,
          SyncOrdersWorker_HiltModule.class,
          UploadLogsWorker_HiltModule.class
      }
  )
  @Singleton
  public abstract static class SingletonC implements CreateMavericksViewModelComponent,
      App_GeneratedInjector,
      FragmentGetContextFix.FragmentGetContextFixEntryPoint,
      HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint,
      ServiceComponentManager.ServiceComponentBuilderEntryPoint,
      SingletonComponent,
      GeneratedComponent {
  }

  @Subcomponent
  @ServiceScoped
  public abstract static class ServiceC implements EndlessSocketService_GeneratedInjector,
      MySocketJobService_GeneratedInjector,
      ServiceComponent,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ServiceComponentBuilder {
    }
  }

  @Subcomponent(
      modules = {
          AppViewModelModule.class,
          PaymentModule.class
      }
  )
  @MavericksViewModelScoped
  public abstract static class MavericksViewModelC implements HiltMavericksEntryPoint,
      MavericksViewModelComponent,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends MavericksViewModelComponentBuilder {
    }
  }

  @Subcomponent(
      modules = {
          ActivityCBuilderModule.class,
          ViewModelCBuilderModule.class,
          HiltWrapper_ActivityRetainedComponentManager_LifecycleModule.class,
          HiltWrapper_SavedStateHandleModule.class
      }
  )
  @ActivityRetainedScoped
  public abstract static class ActivityRetainedC implements ActivityRetainedComponent,
      ActivityComponentManager.ActivityComponentBuilderEntryPoint,
      HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedLifecycleEntryPoint,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ActivityRetainedComponentBuilder {
    }
  }

  @Subcomponent(
      modules = {
          FragmentCBuilderModule.class,
          ViewCBuilderModule.class,
          HiltWrapper_ActivityModule.class,
          HiltWrapper_DefaultViewModelFactories_ActivityModule.class
      }
  )
  @ActivityScoped
  public abstract static class ActivityC implements GuavaOrdersActivity_GeneratedInjector,
      CashPaymentActivity_GeneratedInjector,
      PaymentActivity_GeneratedInjector,
      SumUpPaymentActivity_GeneratedInjector,
      RefundSumUpActivity_GeneratedInjector,
      AreaTableSelectionActivity_GeneratedInjector,
      RewardsActivity_GeneratedInjector,
      SettingsActivity_GeneratedInjector,
      ActivityComponent,
      DefaultViewModelFactories.ActivityEntryPoint,
      HiltWrapper_HiltViewModelFactory_ActivityCreatorEntryPoint,
      FragmentComponentManager.FragmentComponentBuilderEntryPoint,
      ViewComponentManager.ViewComponentBuilderEntryPoint,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ActivityComponentBuilder {
    }
  }

  @Subcomponent(
      modules = HiltWrapper_HiltViewModelFactory_ViewModelModule.class
  )
  @ViewModelScoped
  public abstract static class ViewModelC implements ViewModelComponent,
      HiltViewModelFactory.ViewModelFactoriesEntryPoint,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ViewModelComponentBuilder {
    }
  }

  @Subcomponent
  @ViewScoped
  public abstract static class ViewC implements ViewComponent,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ViewComponentBuilder {
    }
  }

  @Subcomponent(
      modules = ViewWithFragmentCBuilderModule.class
  )
  @FragmentScoped
  public abstract static class FragmentC implements FragmentComponent,
      DefaultViewModelFactories.FragmentEntryPoint,
      ViewComponentManager.ViewWithFragmentComponentBuilderEntryPoint,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends FragmentComponentBuilder {
    }
  }

  @Subcomponent
  @ViewScoped
  public abstract static class ViewWithFragmentC implements ViewWithFragmentComponent,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ViewWithFragmentComponentBuilder {
    }
  }
}
