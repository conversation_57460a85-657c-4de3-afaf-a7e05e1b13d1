package com.thedasagroup.suminative;

import android.app.Activity;
import android.app.Service;
import android.content.Context;
import android.media.AudioManager;
import android.view.View;
import androidx.fragment.app.Fragment;
import androidx.hilt.work.HiltWorkerFactory;
import androidx.hilt.work.WorkerAssistedFactory;
import androidx.hilt.work.WorkerFactoryModule_ProvideFactoryFactory;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import androidx.work.ListenableWorker;
import androidx.work.WorkerParameters;
import com.airbnb.mvrx.MavericksViewModel;
import com.airbnb.mvrx.hilt.AssistedViewModelFactory;
import com.airbnb.mvrx.hilt.MavericksViewModelComponentBuilder;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import com.instacart.truetime.time.TrueTimeImpl;
import com.thedasagroup.suminative.data.database.CategoryRepository;
import com.thedasagroup.suminative.data.database.DatabaseManager;
import com.thedasagroup.suminative.data.database.LocalOrderRepository;
import com.thedasagroup.suminative.data.prefs.Prefs;
import com.thedasagroup.suminative.data.repo.ClockInOutRepository;
import com.thedasagroup.suminative.data.repo.LoginRepository;
import com.thedasagroup.suminative.data.repo.LogsRepository;
import com.thedasagroup.suminative.data.repo.MyGuavaRepository;
import com.thedasagroup.suminative.data.repo.OptionRepository;
import com.thedasagroup.suminative.data.repo.OrdersRepository;
import com.thedasagroup.suminative.data.repo.ProductRepository;
import com.thedasagroup.suminative.data.repo.ReservationsRepository;
import com.thedasagroup.suminative.data.repo.RewardsRepository;
import com.thedasagroup.suminative.data.repo.SalesRepository;
import com.thedasagroup.suminative.data.repo.StockRepository;
import com.thedasagroup.suminative.data.repo.WaitersRepository;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvideChangeStatusUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvideCloseOpenStoreUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvideDownloadProductsUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvideOptionRepositoryFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidePendingOrdersUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvideProductRepositoryFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvideScheduleOrdersPagedUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvideStoreSettingsUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesAcceptDeliveryOrderUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesAcceptOrderWithDelayUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesAddPointsUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesCancelReservationUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesClockInUserTimeUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesClockOutUserTimeUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesCloudPrintUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesCreateReservationUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesEditReservationUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesGetActiveReservationsUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesGetAllCustomersUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesGetAllReservationsUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesGetOrdersUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesGetPosSettingsUsecaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesGetReservationAreasUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesGetReservationTablesUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesGuavaOrderUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesLocalOrdersUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesLoginUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesMyGuavaCheckStatusUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesMyGuavaCreateOrderUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesMyGuavaCreateSessionUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesMyGuavaGetOrderUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesMyGuavaGetTerminalsUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesMyGuavaMakePaymentUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesMyGuavaMakeRefundUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesMyGuavaRefundOrderUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesOrderUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesSalesReportUsecaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesSalesUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesScheduleOrdersUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesStoreUserLoginUseCaseFactory;
import com.thedasagroup.suminative.di.AppUseCaseModule_ProvidesSyncOrdersUseCaseFactory;
import com.thedasagroup.suminative.di.CategoryModule_ProvideCategoryRepositoryFactory;
import com.thedasagroup.suminative.di.CategoryModule_ProvidesCategoriesHelperFactory;
import com.thedasagroup.suminative.di.OrderUseCaseModule_ProvidesOptionDetailsUseCaseFactory;
import com.thedasagroup.suminative.di.OrderUseCaseModule_ProvidesStockUseCaseFactory;
import com.thedasagroup.suminative.di.OrderUseCaseModule_ProvidesUpdateStockUseCaseFactory;
import com.thedasagroup.suminative.di.RepoModule_GetHourUtilsFactory;
import com.thedasagroup.suminative.di.RepoModule_ProvidesAudioManagerFactory;
import com.thedasagroup.suminative.di.RepoModule_ProvidesClockInOutRepositoryFactory;
import com.thedasagroup.suminative.di.RepoModule_ProvidesDatabaseManagerFactory;
import com.thedasagroup.suminative.di.RepoModule_ProvidesLoginRepositoryFactory;
import com.thedasagroup.suminative.di.RepoModule_ProvidesLogsRepositoryFactory;
import com.thedasagroup.suminative.di.RepoModule_ProvidesMyGuavaRepositoryFactory;
import com.thedasagroup.suminative.di.RepoModule_ProvidesOrderLocalRepositoryFactory;
import com.thedasagroup.suminative.di.RepoModule_ProvidesOrderSyncManagerFactory;
import com.thedasagroup.suminative.di.RepoModule_ProvidesOrdersRepositoryFactory;
import com.thedasagroup.suminative.di.RepoModule_ProvidesReservationsRepositoryFactory;
import com.thedasagroup.suminative.di.RepoModule_ProvidesRewardsRepositoryFactory;
import com.thedasagroup.suminative.di.RepoModule_ProvidesSalesRepositoryFactory;
import com.thedasagroup.suminative.di.RepoModule_ProvidesSharedPrefsFactory;
import com.thedasagroup.suminative.di.RepoModule_ProvidesSoundPoolPlayerFactory;
import com.thedasagroup.suminative.di.RepoModule_ProvidesStockRepositoryFactory;
import com.thedasagroup.suminative.di.RepoModule_ProvidesTrueTimeFactory;
import com.thedasagroup.suminative.di.RepoModule_ProvidesWaitersRepositoryFactory;
import com.thedasagroup.suminative.domain.GetPOSSettingsUseCase;
import com.thedasagroup.suminative.domain.cloud_print.CloudPrintUseCase;
import com.thedasagroup.suminative.domain.myguava.MyGuavaCheckStatusUseCase;
import com.thedasagroup.suminative.domain.myguava.MyGuavaCreateOrderUseCase;
import com.thedasagroup.suminative.domain.myguava.MyGuavaCreateRefundOrderUseCase;
import com.thedasagroup.suminative.domain.myguava.MyGuavaCreateSessionUseCase;
import com.thedasagroup.suminative.domain.myguava.MyGuavaGetOrdersUseCase;
import com.thedasagroup.suminative.domain.myguava.MyGuavaGetTerminalsUseCase;
import com.thedasagroup.suminative.domain.myguava.MyGuavaMakePaymentUseCase;
import com.thedasagroup.suminative.domain.myguava.MyGuavaMakeRefundUseCase;
import com.thedasagroup.suminative.domain.orders.GetLocalOrdersUseCase;
import com.thedasagroup.suminative.domain.orders.SyncOrdersUseCase;
import com.thedasagroup.suminative.domain.rewards.AddPointsUseCase;
import com.thedasagroup.suminative.domain.rewards.GetAllCustomersUseCase;
import com.thedasagroup.suminative.domain.sales_report.GetSalesReportUseCase;
import com.thedasagroup.suminative.ui.common.CommonState;
import com.thedasagroup.suminative.ui.common.CommonViewModel;
import com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersActivity;
import com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersState;
import com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersViewModel;
import com.thedasagroup.suminative.ui.local_orders.LocalOrdersActivity;
import com.thedasagroup.suminative.ui.local_orders.LocalOrdersState;
import com.thedasagroup.suminative.ui.local_orders.LocalOrdersViewModel;
import com.thedasagroup.suminative.ui.login.ClockInUserTimeUseCase;
import com.thedasagroup.suminative.ui.login.ClockOutUserTimeUseCase;
import com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase;
import com.thedasagroup.suminative.ui.login.LoginScreenState;
import com.thedasagroup.suminative.ui.login.LoginScreenViewModel;
import com.thedasagroup.suminative.ui.login.LoginUseCase;
import com.thedasagroup.suminative.ui.login.StoreUserLoginUseCase;
import com.thedasagroup.suminative.ui.orders.AcceptOrderWithDelayUseCase;
import com.thedasagroup.suminative.ui.orders.ChangeStatusAndOrdersUseCase;
import com.thedasagroup.suminative.ui.orders.ChangeStatusUseCase;
import com.thedasagroup.suminative.ui.orders.CloseOpenStoreUseCase;
import com.thedasagroup.suminative.ui.orders.GetOrdersUseCase;
import com.thedasagroup.suminative.ui.orders.GetPendingOrdersPagedUseCase;
import com.thedasagroup.suminative.ui.orders.GetScheduleOrdersPagedUseCase;
import com.thedasagroup.suminative.ui.orders.GetScheduleOrdersUseCase;
import com.thedasagroup.suminative.ui.orders.OrderScreenViewModel;
import com.thedasagroup.suminative.ui.orders.OrderState;
import com.thedasagroup.suminative.ui.payment.CashPaymentActivity;
import com.thedasagroup.suminative.ui.payment.PaymentActivity;
import com.thedasagroup.suminative.ui.payment.PaymentState;
import com.thedasagroup.suminative.ui.payment.PaymentViewModel;
import com.thedasagroup.suminative.ui.payment.SumUpPaymentActivity;
import com.thedasagroup.suminative.ui.products.DownloadProductsState;
import com.thedasagroup.suminative.ui.products.DownloadProductsUseCase;
import com.thedasagroup.suminative.ui.products.DownloadProductsViewModel;
import com.thedasagroup.suminative.ui.products.OptionDetailsUseCase;
import com.thedasagroup.suminative.ui.products.OrderUseCase;
import com.thedasagroup.suminative.ui.products.PlaceOnlineOrderUseCase;
import com.thedasagroup.suminative.ui.products.ProductsScreenState;
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel;
import com.thedasagroup.suminative.ui.refund.RefundSumUpActivity;
import com.thedasagroup.suminative.ui.refund.RefundSumUpState;
import com.thedasagroup.suminative.ui.refund.RefundSumUpViewModel;
import com.thedasagroup.suminative.ui.reservations.AreaTableSelectionActivity;
import com.thedasagroup.suminative.ui.reservations.CancelReservationUseCase;
import com.thedasagroup.suminative.ui.reservations.CreateReservationUseCase;
import com.thedasagroup.suminative.ui.reservations.EditReservationUseCase;
import com.thedasagroup.suminative.ui.reservations.GetActiveReservationsUseCase;
import com.thedasagroup.suminative.ui.reservations.GetAllReservationsUseCase;
import com.thedasagroup.suminative.ui.reservations.GetReservationAreasUseCase;
import com.thedasagroup.suminative.ui.reservations.GetReservationTablesUseCase;
import com.thedasagroup.suminative.ui.reservations.ReservationsState;
import com.thedasagroup.suminative.ui.reservations.ReservationsViewModel;
import com.thedasagroup.suminative.ui.rewards.RewardsActivity;
import com.thedasagroup.suminative.ui.rewards.RewardsState;
import com.thedasagroup.suminative.ui.rewards.RewardsViewModel;
import com.thedasagroup.suminative.ui.sales.TotalSalesUseCase;
import com.thedasagroup.suminative.ui.service.EndlessSocketService;
import com.thedasagroup.suminative.ui.service.EndlessSocketService_MembersInjector;
import com.thedasagroup.suminative.ui.service.MySocketJobService;
import com.thedasagroup.suminative.ui.service.MySocketJobService_MembersInjector;
import com.thedasagroup.suminative.ui.settings.SettingsActivity;
import com.thedasagroup.suminative.ui.stock.CategorySortingHelper;
import com.thedasagroup.suminative.ui.stock.ChangeStockUseCase;
import com.thedasagroup.suminative.ui.stock.StockScreenState;
import com.thedasagroup.suminative.ui.stock.StockScreenViewModel;
import com.thedasagroup.suminative.ui.stock.StockUseCase;
import com.thedasagroup.suminative.ui.user_profile.SelectUserProfileState;
import com.thedasagroup.suminative.ui.user_profile.SelectUserProfileViewModel;
import com.thedasagroup.suminative.ui.utils.SoundPoolPlayer;
import com.thedasagroup.suminative.work.OrderSyncManager;
import com.thedasagroup.suminative.work.SyncOrdersWorker;
import com.thedasagroup.suminative.work.SyncOrdersWorker_AssistedFactory;
import com.thedasagroup.suminative.work.UploadLogsWorker;
import com.thedasagroup.suminative.work.UploadLogsWorker_AssistedFactory;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.managers.SavedStateHandleHolder;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DoubleCheck;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.SingleCheck;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DaggerApp_HiltComponents_SingletonC {
  private DaggerApp_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private ApplicationContextModule applicationContextModule;

    private Builder() {
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    public App_HiltComponents.SingletonC build() {
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      return new SingletonCImpl(applicationContextModule);
    }
  }

  private static final class MavericksViewModelCBuilder implements App_HiltComponents.MavericksViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private MavericksViewModelCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public App_HiltComponents.MavericksViewModelC build() {
      return new MavericksViewModelCImpl(singletonCImpl);
    }
  }

  private static final class ActivityRetainedCBuilder implements App_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private SavedStateHandleHolder savedStateHandleHolder;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ActivityRetainedCBuilder savedStateHandleHolder(
        SavedStateHandleHolder savedStateHandleHolder) {
      this.savedStateHandleHolder = Preconditions.checkNotNull(savedStateHandleHolder);
      return this;
    }

    @Override
    public App_HiltComponents.ActivityRetainedC build() {
      Preconditions.checkBuilderRequirement(savedStateHandleHolder, SavedStateHandleHolder.class);
      return new ActivityRetainedCImpl(singletonCImpl, savedStateHandleHolder);
    }
  }

  private static final class ActivityCBuilder implements App_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public App_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements App_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public App_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements App_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public App_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements App_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public App_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements App_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public App_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements App_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public App_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class MavericksViewModelCImpl extends App_HiltComponents.MavericksViewModelC {
    private final SingletonCImpl singletonCImpl;

    private final MavericksViewModelCImpl mavericksViewModelCImpl = this;

    Provider<LoginScreenViewModel.Factory> factoryProvider;

    Provider<OrderScreenViewModel.Factory> factoryProvider2;

    Provider<StockScreenViewModel.Factory> factoryProvider3;

    Provider<ProductsScreenViewModel.Factory> factoryProvider4;

    Provider<DownloadProductsViewModel.Factory> factoryProvider5;

    Provider<GuavaOrdersViewModel.Factory> factoryProvider6;

    Provider<SelectUserProfileViewModel.Factory> factoryProvider7;

    Provider<LocalOrdersViewModel.Factory> factoryProvider8;

    Provider<CommonViewModel.Factory> factoryProvider9;

    Provider<ReservationsViewModel.Factory> factoryProvider10;

    Provider<RefundSumUpViewModel.Factory> factoryProvider11;

    Provider<RewardsViewModel.Factory> factoryProvider12;

    Provider<PaymentViewModel.Factory> factoryProvider13;

    MavericksViewModelCImpl(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;

      initialize();

    }

    @SuppressWarnings("unchecked")
    private void initialize() {
      this.factoryProvider = SingleCheck.provider(new SwitchingProvider<LoginScreenViewModel.Factory>(singletonCImpl, mavericksViewModelCImpl, 0));
      this.factoryProvider2 = SingleCheck.provider(new SwitchingProvider<OrderScreenViewModel.Factory>(singletonCImpl, mavericksViewModelCImpl, 1));
      this.factoryProvider3 = SingleCheck.provider(new SwitchingProvider<StockScreenViewModel.Factory>(singletonCImpl, mavericksViewModelCImpl, 2));
      this.factoryProvider4 = SingleCheck.provider(new SwitchingProvider<ProductsScreenViewModel.Factory>(singletonCImpl, mavericksViewModelCImpl, 3));
      this.factoryProvider5 = SingleCheck.provider(new SwitchingProvider<DownloadProductsViewModel.Factory>(singletonCImpl, mavericksViewModelCImpl, 4));
      this.factoryProvider6 = SingleCheck.provider(new SwitchingProvider<GuavaOrdersViewModel.Factory>(singletonCImpl, mavericksViewModelCImpl, 5));
      this.factoryProvider7 = SingleCheck.provider(new SwitchingProvider<SelectUserProfileViewModel.Factory>(singletonCImpl, mavericksViewModelCImpl, 6));
      this.factoryProvider8 = SingleCheck.provider(new SwitchingProvider<LocalOrdersViewModel.Factory>(singletonCImpl, mavericksViewModelCImpl, 7));
      this.factoryProvider9 = SingleCheck.provider(new SwitchingProvider<CommonViewModel.Factory>(singletonCImpl, mavericksViewModelCImpl, 8));
      this.factoryProvider10 = SingleCheck.provider(new SwitchingProvider<ReservationsViewModel.Factory>(singletonCImpl, mavericksViewModelCImpl, 9));
      this.factoryProvider11 = SingleCheck.provider(new SwitchingProvider<RefundSumUpViewModel.Factory>(singletonCImpl, mavericksViewModelCImpl, 10));
      this.factoryProvider12 = SingleCheck.provider(new SwitchingProvider<RewardsViewModel.Factory>(singletonCImpl, mavericksViewModelCImpl, 11));
      this.factoryProvider13 = SingleCheck.provider(new SwitchingProvider<PaymentViewModel.Factory>(singletonCImpl, mavericksViewModelCImpl, 12));
    }

    @Override
    public Map<Class<? extends MavericksViewModel<?>>, AssistedViewModelFactory<?, ?>> getViewModelFactories(
        ) {
      return ImmutableMap.<Class<? extends MavericksViewModel<?>>, AssistedViewModelFactory<?, ?>>builderWithExpectedSize(13).put(LoginScreenViewModel.class, factoryProvider.get()).put(OrderScreenViewModel.class, factoryProvider2.get()).put(StockScreenViewModel.class, factoryProvider3.get()).put(ProductsScreenViewModel.class, factoryProvider4.get()).put(DownloadProductsViewModel.class, factoryProvider5.get()).put(GuavaOrdersViewModel.class, factoryProvider6.get()).put(SelectUserProfileViewModel.class, factoryProvider7.get()).put(LocalOrdersViewModel.class, factoryProvider8.get()).put(CommonViewModel.class, factoryProvider9.get()).put(ReservationsViewModel.class, factoryProvider10.get()).put(RefundSumUpViewModel.class, factoryProvider11.get()).put(RewardsViewModel.class, factoryProvider12.get()).put(PaymentViewModel.class, factoryProvider13.get()).build();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final MavericksViewModelCImpl mavericksViewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl,
          MavericksViewModelCImpl mavericksViewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.mavericksViewModelCImpl = mavericksViewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.thedasagroup.suminative.ui.login.LoginScreenViewModel.Factory
          return (T) new LoginScreenViewModel.Factory() {
            @Override
            public LoginScreenViewModel create(LoginScreenState state) {
              return new LoginScreenViewModel(state, singletonCImpl.providesLoginUseCaseProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());
            }
          };

          case 1: // com.thedasagroup.suminative.ui.orders.OrderScreenViewModel.Factory
          return (T) new OrderScreenViewModel.Factory() {
            @Override
            public OrderScreenViewModel create(OrderState state2) {
              return new OrderScreenViewModel(state2, singletonCImpl.providesGetOrdersUseCaseProvider.get(), singletonCImpl.providePendingOrdersUseCaseProvider.get(), singletonCImpl.providesScheduleOrdersUseCaseProvider.get(), singletonCImpl.provideScheduleOrdersPagedUseCaseProvider.get(), singletonCImpl.provideChangeStatusUseCaseProvider.get(), singletonCImpl.providesAcceptDeliveryOrderUseCaseProvider.get(), singletonCImpl.providesAcceptOrderWithDelayUseCaseProvider.get(), singletonCImpl.providesTrueTimeProvider.get(), singletonCImpl.providesOrdersRepositoryProvider.get(), singletonCImpl.provideStoreSettingsUseCaseProvider.get(), singletonCImpl.provideCloseOpenStoreUseCaseProvider.get(), singletonCImpl.providesSharedPrefsProvider.get(), singletonCImpl.providesSoundPoolPlayerProvider.get(), singletonCImpl.providesAudioManagerProvider.get(), singletonCImpl.providesGetPosSettingsUsecaseProvider.get());
            }
          };

          case 2: // com.thedasagroup.suminative.ui.stock.StockScreenViewModel.Factory
          return (T) new StockScreenViewModel.Factory() {
            @Override
            public StockScreenViewModel create(StockScreenState state3) {
              return new StockScreenViewModel(state3, singletonCImpl.providesSharedPrefsProvider.get(), singletonCImpl.providesStockUseCaseProvider.get(), singletonCImpl.providesUpdateStockUseCaseProvider.get(), singletonCImpl.providesStockRepositoryProvider.get());
            }
          };

          case 3: // com.thedasagroup.suminative.ui.products.ProductsScreenViewModel.Factory
          return (T) new ProductsScreenViewModel.Factory() {
            @Override
            public ProductsScreenViewModel create(ProductsScreenState state4) {
              return new ProductsScreenViewModel(state4, singletonCImpl.providesSharedPrefsProvider.get(), singletonCImpl.providesStockUseCaseProvider.get(), singletonCImpl.providesGuavaOrderUseCaseProvider.get(), singletonCImpl.providesOrderUseCaseProvider.get(), singletonCImpl.providesCloudPrintUseCaseProvider.get(), singletonCImpl.providesOptionDetailsUseCaseProvider.get(), singletonCImpl.providesSalesUseCaseProvider.get(), singletonCImpl.providesSalesReportUsecaseProvider.get(), singletonCImpl.providesTrueTimeProvider.get(), singletonCImpl.provideProductRepositoryProvider.get(), singletonCImpl.downloadProductsUseCase(), singletonCImpl.providesOrderSyncManagerProvider.get());
            }
          };

          case 4: // com.thedasagroup.suminative.ui.products.DownloadProductsViewModel.Factory
          return (T) new DownloadProductsViewModel.Factory() {
            @Override
            public DownloadProductsViewModel create(DownloadProductsState state5) {
              return new DownloadProductsViewModel(state5, singletonCImpl.downloadProductsUseCase());
            }
          };

          case 5: // com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersViewModel.Factory
          return (T) new GuavaOrdersViewModel.Factory() {
            @Override
            public GuavaOrdersViewModel create(GuavaOrdersState initialState) {
              return new GuavaOrdersViewModel(initialState, singletonCImpl.providesMyGuavaGetOrderUseCaseProvider.get(), singletonCImpl.providesMyGuavaRepositoryProvider.get());
            }
          };

          case 6: // com.thedasagroup.suminative.ui.user_profile.SelectUserProfileViewModel.Factory
          return (T) new SelectUserProfileViewModel.Factory() {
            @Override
            public SelectUserProfileViewModel create(SelectUserProfileState state6) {
              return new SelectUserProfileViewModel(state6, singletonCImpl.providesWaitersRepositoryProvider.get(), singletonCImpl.providesLoginUseCaseProvider.get(), singletonCImpl.providesStoreUserLoginUseCaseProvider.get(), singletonCImpl.providesClockInUserTimeUseCaseProvider.get(), singletonCImpl.providesClockOutUserTimeUseCaseProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());
            }
          };

          case 7: // com.thedasagroup.suminative.ui.local_orders.LocalOrdersViewModel.Factory
          return (T) new LocalOrdersViewModel.Factory() {
            @Override
            public LocalOrdersViewModel create(LocalOrdersState state7) {
              return new LocalOrdersViewModel(state7, singletonCImpl.providesLocalOrdersUseCaseProvider.get(), singletonCImpl.providesSyncOrdersUseCaseProvider.get());
            }
          };

          case 8: // com.thedasagroup.suminative.ui.common.CommonViewModel.Factory
          return (T) new CommonViewModel.Factory() {
            @Override
            public CommonViewModel create(CommonState state8) {
              return new CommonViewModel(state8, singletonCImpl.providesLoginUseCaseProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());
            }
          };

          case 9: // com.thedasagroup.suminative.ui.reservations.ReservationsViewModel.Factory
          return (T) new ReservationsViewModel.Factory() {
            @Override
            public ReservationsViewModel create(ReservationsState state9) {
              return new ReservationsViewModel(state9, singletonCImpl.providesGetActiveReservationsUseCaseProvider.get(), singletonCImpl.providesGetAllReservationsUseCaseProvider.get(), singletonCImpl.providesCreateReservationUseCaseProvider.get(), singletonCImpl.providesEditReservationUseCaseProvider.get(), singletonCImpl.providesCancelReservationUseCaseProvider.get(), singletonCImpl.providesGetReservationAreasUseCaseProvider.get(), singletonCImpl.providesGetReservationTablesUseCaseProvider.get());
            }
          };

          case 10: // com.thedasagroup.suminative.ui.refund.RefundSumUpViewModel.Factory
          return (T) new RefundSumUpViewModel.Factory() {
            @Override
            public RefundSumUpViewModel create(RefundSumUpState state10) {
              return new RefundSumUpViewModel(state10, singletonCImpl.providePendingOrdersUseCaseProvider.get());
            }
          };

          case 11: // com.thedasagroup.suminative.ui.rewards.RewardsViewModel.Factory
          return (T) new RewardsViewModel.Factory() {
            @Override
            public RewardsViewModel create(RewardsState state11) {
              return new RewardsViewModel(state11, singletonCImpl.providesGetAllCustomersUseCaseProvider.get(), singletonCImpl.providesAddPointsUseCaseProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());
            }
          };

          case 12: // com.thedasagroup.suminative.ui.payment.PaymentViewModel.Factory
          return (T) new PaymentViewModel.Factory() {
            @Override
            public PaymentViewModel create(PaymentState initialState2) {
              return new PaymentViewModel(initialState2, singletonCImpl.providesMyGuavaCreateOrderUseCaseProvider.get(), singletonCImpl.providesMyGuavaGetTerminalsUseCaseProvider.get(), singletonCImpl.providesMyGuavaCreateSessionUseCaseProvider.get(), singletonCImpl.providesMyGuavaMakePaymentUseCaseProvider.get(), singletonCImpl.providesMyGuavaMakeRefundUseCaseProvider.get(), singletonCImpl.providesMyGuavaCheckStatusUseCaseProvider.get(), singletonCImpl.providesOrderUseCaseProvider.get(), singletonCImpl.providesGuavaOrderUseCaseProvider.get(), singletonCImpl.providesMyGuavaRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());
            }
          };

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ViewWithFragmentCImpl extends App_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends App_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    FragmentCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }
  }

  private static final class ViewCImpl extends App_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends App_HiltComponents.ActivityC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    ActivityCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public void injectGuavaOrdersActivity(GuavaOrdersActivity arg0) {
    }

    @Override
    public void injectLocalOrdersActivity(LocalOrdersActivity arg0) {
    }

    @Override
    public void injectCashPaymentActivity(CashPaymentActivity arg0) {
    }

    @Override
    public void injectPaymentActivity(PaymentActivity arg0) {
    }

    @Override
    public void injectSumUpPaymentActivity(SumUpPaymentActivity arg0) {
    }

    @Override
    public void injectRefundSumUpActivity(RefundSumUpActivity arg0) {
    }

    @Override
    public void injectAreaTableSelectionActivity(AreaTableSelectionActivity arg0) {
    }

    @Override
    public void injectRewardsActivity(RewardsActivity arg0) {
    }

    @Override
    public void injectSettingsActivity(SettingsActivity arg0) {
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(ImmutableMap.<Class<?>, Boolean>of(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Map<Class<?>, Boolean> getViewModelKeys() {
      return ImmutableMap.<Class<?>, Boolean>of();
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }
  }

  private static final class ViewModelCImpl extends App_HiltComponents.ViewModelC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    ViewModelCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        SavedStateHandle savedStateHandleParam, ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public Map<Class<?>, javax.inject.Provider<ViewModel>> getHiltViewModelMap() {
      return ImmutableMap.<Class<?>, javax.inject.Provider<ViewModel>>of();
    }

    @Override
    public Map<Class<?>, Object> getHiltViewModelAssistedMap() {
      return ImmutableMap.<Class<?>, Object>of();
    }
  }

  private static final class ActivityRetainedCImpl extends App_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    ActivityRetainedCImpl(SingletonCImpl singletonCImpl,
        SavedStateHandleHolder savedStateHandleHolderParam) {
      this.singletonCImpl = singletonCImpl;

      initialize(savedStateHandleHolderParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandleHolder savedStateHandleHolderParam) {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends App_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }

    @Override
    public void injectEndlessSocketService(EndlessSocketService arg0) {
      injectEndlessSocketService2(arg0);
    }

    @Override
    public void injectMySocketJobService(MySocketJobService arg0) {
      injectMySocketJobService2(arg0);
    }

    @CanIgnoreReturnValue
    private EndlessSocketService injectEndlessSocketService2(EndlessSocketService instance) {
      EndlessSocketService_MembersInjector.injectPrefs(instance, singletonCImpl.providesSharedPrefsProvider.get());
      EndlessSocketService_MembersInjector.injectOrderRepository(instance, singletonCImpl.providesOrdersRepositoryProvider.get());
      EndlessSocketService_MembersInjector.injectGetStoreSettings(instance, singletonCImpl.provideStoreSettingsUseCaseProvider.get());
      EndlessSocketService_MembersInjector.injectTrueTimeImpl(instance, singletonCImpl.providesTrueTimeProvider.get());
      EndlessSocketService_MembersInjector.injectSoundPoolPlayer(instance, singletonCImpl.providesSoundPoolPlayerProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private MySocketJobService injectMySocketJobService2(MySocketJobService instance2) {
      MySocketJobService_MembersInjector.injectPrefs(instance2, singletonCImpl.providesSharedPrefsProvider.get());
      return instance2;
    }
  }

  private static final class SingletonCImpl extends App_HiltComponents.SingletonC {
    private final ApplicationContextModule applicationContextModule;

    private final SingletonCImpl singletonCImpl = this;

    Provider<Prefs> providesSharedPrefsProvider;

    Provider<HourUtils> getHourUtilsProvider;

    Provider<TrueTimeImpl> providesTrueTimeProvider;

    Provider<DatabaseManager> providesDatabaseManagerProvider;

    Provider<LocalOrderRepository> providesOrderLocalRepositoryProvider;

    Provider<StockRepository> providesStockRepositoryProvider;

    Provider<SyncOrdersUseCase> providesSyncOrdersUseCaseProvider;

    Provider<SyncOrdersWorker_AssistedFactory> syncOrdersWorker_AssistedFactoryProvider;

    Provider<LogsRepository> providesLogsRepositoryProvider;

    Provider<UploadLogsWorker_AssistedFactory> uploadLogsWorker_AssistedFactoryProvider;

    Provider<OrderSyncManager> providesOrderSyncManagerProvider;

    Provider<LoginRepository> providesLoginRepositoryProvider;

    Provider<LoginUseCase> providesLoginUseCaseProvider;

    Provider<OrdersRepository> providesOrdersRepositoryProvider;

    Provider<GetOrdersUseCase> providesGetOrdersUseCaseProvider;

    Provider<GetPendingOrdersPagedUseCase> providePendingOrdersUseCaseProvider;

    Provider<GetScheduleOrdersUseCase> providesScheduleOrdersUseCaseProvider;

    Provider<GetScheduleOrdersPagedUseCase> provideScheduleOrdersPagedUseCaseProvider;

    Provider<ChangeStatusUseCase> provideChangeStatusUseCaseProvider;

    Provider<ChangeStatusAndOrdersUseCase> providesAcceptDeliveryOrderUseCaseProvider;

    Provider<GetStoreSettingsUseCase> provideStoreSettingsUseCaseProvider;

    Provider<AcceptOrderWithDelayUseCase> providesAcceptOrderWithDelayUseCaseProvider;

    Provider<CloseOpenStoreUseCase> provideCloseOpenStoreUseCaseProvider;

    Provider<SoundPoolPlayer> providesSoundPoolPlayerProvider;

    Provider<AudioManager> providesAudioManagerProvider;

    Provider<GetPOSSettingsUseCase> providesGetPosSettingsUsecaseProvider;

    Provider<ProductRepository> provideProductRepositoryProvider;

    Provider<CategoryRepository> provideCategoryRepositoryProvider;

    Provider<CategorySortingHelper> providesCategoriesHelperProvider;

    Provider<StockUseCase> providesStockUseCaseProvider;

    Provider<ChangeStockUseCase> providesUpdateStockUseCaseProvider;

    Provider<MyGuavaRepository> providesMyGuavaRepositoryProvider;

    Provider<PlaceOnlineOrderUseCase> providesGuavaOrderUseCaseProvider;

    Provider<OrderUseCase> providesOrderUseCaseProvider;

    Provider<CloudPrintUseCase> providesCloudPrintUseCaseProvider;

    Provider<OptionRepository> provideOptionRepositoryProvider;

    Provider<OptionDetailsUseCase> providesOptionDetailsUseCaseProvider;

    Provider<SalesRepository> providesSalesRepositoryProvider;

    Provider<TotalSalesUseCase> providesSalesUseCaseProvider;

    Provider<GetSalesReportUseCase> providesSalesReportUsecaseProvider;

    Provider<MyGuavaGetOrdersUseCase> providesMyGuavaGetOrderUseCaseProvider;

    Provider<WaitersRepository> providesWaitersRepositoryProvider;

    Provider<ClockInOutRepository> providesClockInOutRepositoryProvider;

    Provider<StoreUserLoginUseCase> providesStoreUserLoginUseCaseProvider;

    Provider<ClockInUserTimeUseCase> providesClockInUserTimeUseCaseProvider;

    Provider<ClockOutUserTimeUseCase> providesClockOutUserTimeUseCaseProvider;

    Provider<GetLocalOrdersUseCase> providesLocalOrdersUseCaseProvider;

    Provider<ReservationsRepository> providesReservationsRepositoryProvider;

    Provider<GetActiveReservationsUseCase> providesGetActiveReservationsUseCaseProvider;

    Provider<GetAllReservationsUseCase> providesGetAllReservationsUseCaseProvider;

    Provider<CreateReservationUseCase> providesCreateReservationUseCaseProvider;

    Provider<EditReservationUseCase> providesEditReservationUseCaseProvider;

    Provider<CancelReservationUseCase> providesCancelReservationUseCaseProvider;

    Provider<GetReservationAreasUseCase> providesGetReservationAreasUseCaseProvider;

    Provider<GetReservationTablesUseCase> providesGetReservationTablesUseCaseProvider;

    Provider<RewardsRepository> providesRewardsRepositoryProvider;

    Provider<GetAllCustomersUseCase> providesGetAllCustomersUseCaseProvider;

    Provider<AddPointsUseCase> providesAddPointsUseCaseProvider;

    Provider<MyGuavaCreateOrderUseCase> providesMyGuavaCreateOrderUseCaseProvider;

    Provider<MyGuavaGetTerminalsUseCase> providesMyGuavaGetTerminalsUseCaseProvider;

    Provider<MyGuavaCreateSessionUseCase> providesMyGuavaCreateSessionUseCaseProvider;

    Provider<MyGuavaMakePaymentUseCase> providesMyGuavaMakePaymentUseCaseProvider;

    Provider<MyGuavaCreateRefundOrderUseCase> providesMyGuavaRefundOrderUseCaseProvider;

    Provider<MyGuavaMakeRefundUseCase> providesMyGuavaMakeRefundUseCaseProvider;

    Provider<MyGuavaCheckStatusUseCase> providesMyGuavaCheckStatusUseCaseProvider;

    SingletonCImpl(ApplicationContextModule applicationContextModuleParam) {
      this.applicationContextModule = applicationContextModuleParam;
      initialize(applicationContextModuleParam);
      initialize2(applicationContextModuleParam);
      initialize3(applicationContextModuleParam);

    }

    Map<String, javax.inject.Provider<WorkerAssistedFactory<? extends ListenableWorker>>> mapOfStringAndProviderOfWorkerAssistedFactoryOf(
        ) {
      return ImmutableMap.<String, javax.inject.Provider<WorkerAssistedFactory<? extends ListenableWorker>>>of("com.thedasagroup.suminative.work.SyncOrdersWorker", ((Provider) (syncOrdersWorker_AssistedFactoryProvider)), "com.thedasagroup.suminative.work.UploadLogsWorker", ((Provider) (uploadLogsWorker_AssistedFactoryProvider)));
    }

    HiltWorkerFactory hiltWorkerFactory() {
      return WorkerFactoryModule_ProvideFactoryFactory.provideFactory(mapOfStringAndProviderOfWorkerAssistedFactoryOf());
    }

    DownloadProductsUseCase downloadProductsUseCase() {
      return AppUseCaseModule_ProvideDownloadProductsUseCaseFactory.provideDownloadProductsUseCase(providesStockRepositoryProvider.get(), provideProductRepositoryProvider.get(), provideOptionRepositoryProvider.get(), provideCategoryRepositoryProvider.get(), providesSharedPrefsProvider.get());
    }

    @SuppressWarnings("unchecked")
    private void initialize(final ApplicationContextModule applicationContextModuleParam) {
      this.providesSharedPrefsProvider = DoubleCheck.provider(new SwitchingProvider<Prefs>(singletonCImpl, 0));
      this.getHourUtilsProvider = DoubleCheck.provider(new SwitchingProvider<HourUtils>(singletonCImpl, 1));
      this.providesTrueTimeProvider = DoubleCheck.provider(new SwitchingProvider<TrueTimeImpl>(singletonCImpl, 2));
      this.providesDatabaseManagerProvider = DoubleCheck.provider(new SwitchingProvider<DatabaseManager>(singletonCImpl, 6));
      this.providesOrderLocalRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<LocalOrderRepository>(singletonCImpl, 5));
      this.providesStockRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<StockRepository>(singletonCImpl, 7));
      this.providesSyncOrdersUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<SyncOrdersUseCase>(singletonCImpl, 4));
      this.syncOrdersWorker_AssistedFactoryProvider = SingleCheck.provider(new SwitchingProvider<SyncOrdersWorker_AssistedFactory>(singletonCImpl, 3));
      this.providesLogsRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<LogsRepository>(singletonCImpl, 9));
      this.uploadLogsWorker_AssistedFactoryProvider = SingleCheck.provider(new SwitchingProvider<UploadLogsWorker_AssistedFactory>(singletonCImpl, 8));
      this.providesOrderSyncManagerProvider = DoubleCheck.provider(new SwitchingProvider<OrderSyncManager>(singletonCImpl, 10));
      this.providesLoginRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<LoginRepository>(singletonCImpl, 12));
      this.providesLoginUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<LoginUseCase>(singletonCImpl, 11));
      this.providesOrdersRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<OrdersRepository>(singletonCImpl, 14));
      this.providesGetOrdersUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetOrdersUseCase>(singletonCImpl, 13));
      this.providePendingOrdersUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetPendingOrdersPagedUseCase>(singletonCImpl, 15));
      this.providesScheduleOrdersUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetScheduleOrdersUseCase>(singletonCImpl, 16));
      this.provideScheduleOrdersPagedUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetScheduleOrdersPagedUseCase>(singletonCImpl, 17));
      this.provideChangeStatusUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<ChangeStatusUseCase>(singletonCImpl, 18));
      this.providesAcceptDeliveryOrderUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<ChangeStatusAndOrdersUseCase>(singletonCImpl, 19));
      this.provideStoreSettingsUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetStoreSettingsUseCase>(singletonCImpl, 21));
      this.providesAcceptOrderWithDelayUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<AcceptOrderWithDelayUseCase>(singletonCImpl, 20));
      this.provideCloseOpenStoreUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<CloseOpenStoreUseCase>(singletonCImpl, 22));
      this.providesSoundPoolPlayerProvider = DoubleCheck.provider(new SwitchingProvider<SoundPoolPlayer>(singletonCImpl, 23));
      this.providesAudioManagerProvider = DoubleCheck.provider(new SwitchingProvider<AudioManager>(singletonCImpl, 24));
    }

    @SuppressWarnings("unchecked")
    private void initialize2(final ApplicationContextModule applicationContextModuleParam) {
      this.providesGetPosSettingsUsecaseProvider = DoubleCheck.provider(new SwitchingProvider<GetPOSSettingsUseCase>(singletonCImpl, 25));
      this.provideProductRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<ProductRepository>(singletonCImpl, 27));
      this.provideCategoryRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<CategoryRepository>(singletonCImpl, 29));
      this.providesCategoriesHelperProvider = DoubleCheck.provider(new SwitchingProvider<CategorySortingHelper>(singletonCImpl, 28));
      this.providesStockUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<StockUseCase>(singletonCImpl, 26));
      this.providesUpdateStockUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<ChangeStockUseCase>(singletonCImpl, 30));
      this.providesMyGuavaRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<MyGuavaRepository>(singletonCImpl, 32));
      this.providesGuavaOrderUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<PlaceOnlineOrderUseCase>(singletonCImpl, 31));
      this.providesOrderUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<OrderUseCase>(singletonCImpl, 33));
      this.providesCloudPrintUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<CloudPrintUseCase>(singletonCImpl, 34));
      this.provideOptionRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<OptionRepository>(singletonCImpl, 36));
      this.providesOptionDetailsUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<OptionDetailsUseCase>(singletonCImpl, 35));
      this.providesSalesRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<SalesRepository>(singletonCImpl, 38));
      this.providesSalesUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<TotalSalesUseCase>(singletonCImpl, 37));
      this.providesSalesReportUsecaseProvider = DoubleCheck.provider(new SwitchingProvider<GetSalesReportUseCase>(singletonCImpl, 39));
      this.providesMyGuavaGetOrderUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<MyGuavaGetOrdersUseCase>(singletonCImpl, 40));
      this.providesWaitersRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<WaitersRepository>(singletonCImpl, 41));
      this.providesClockInOutRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<ClockInOutRepository>(singletonCImpl, 43));
      this.providesStoreUserLoginUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<StoreUserLoginUseCase>(singletonCImpl, 42));
      this.providesClockInUserTimeUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<ClockInUserTimeUseCase>(singletonCImpl, 44));
      this.providesClockOutUserTimeUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<ClockOutUserTimeUseCase>(singletonCImpl, 45));
      this.providesLocalOrdersUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetLocalOrdersUseCase>(singletonCImpl, 46));
      this.providesReservationsRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<ReservationsRepository>(singletonCImpl, 48));
      this.providesGetActiveReservationsUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetActiveReservationsUseCase>(singletonCImpl, 47));
      this.providesGetAllReservationsUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetAllReservationsUseCase>(singletonCImpl, 49));
    }

    @SuppressWarnings("unchecked")
    private void initialize3(final ApplicationContextModule applicationContextModuleParam) {
      this.providesCreateReservationUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<CreateReservationUseCase>(singletonCImpl, 50));
      this.providesEditReservationUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<EditReservationUseCase>(singletonCImpl, 51));
      this.providesCancelReservationUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<CancelReservationUseCase>(singletonCImpl, 52));
      this.providesGetReservationAreasUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetReservationAreasUseCase>(singletonCImpl, 53));
      this.providesGetReservationTablesUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetReservationTablesUseCase>(singletonCImpl, 54));
      this.providesRewardsRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<RewardsRepository>(singletonCImpl, 56));
      this.providesGetAllCustomersUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetAllCustomersUseCase>(singletonCImpl, 55));
      this.providesAddPointsUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<AddPointsUseCase>(singletonCImpl, 57));
      this.providesMyGuavaCreateOrderUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<MyGuavaCreateOrderUseCase>(singletonCImpl, 58));
      this.providesMyGuavaGetTerminalsUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<MyGuavaGetTerminalsUseCase>(singletonCImpl, 59));
      this.providesMyGuavaCreateSessionUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<MyGuavaCreateSessionUseCase>(singletonCImpl, 60));
      this.providesMyGuavaMakePaymentUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<MyGuavaMakePaymentUseCase>(singletonCImpl, 61));
      this.providesMyGuavaRefundOrderUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<MyGuavaCreateRefundOrderUseCase>(singletonCImpl, 63));
      this.providesMyGuavaMakeRefundUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<MyGuavaMakeRefundUseCase>(singletonCImpl, 62));
      this.providesMyGuavaCheckStatusUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<MyGuavaCheckStatusUseCase>(singletonCImpl, 64));
    }

    @Override
    public MavericksViewModelComponentBuilder mavericksViewModelComponentBuilder() {
      return new MavericksViewModelCBuilder(singletonCImpl);
    }

    @Override
    public void injectApp(App app) {
      injectApp2(app);
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return ImmutableSet.<Boolean>of();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    @CanIgnoreReturnValue
    private App injectApp2(App instance) {
      App_MembersInjector.injectPrefs(instance, providesSharedPrefsProvider.get());
      App_MembersInjector.injectHourUtils(instance, getHourUtilsProvider.get());
      App_MembersInjector.injectTrueTime(instance, providesTrueTimeProvider.get());
      App_MembersInjector.injectWorkerFactory(instance, hiltWorkerFactory());
      App_MembersInjector.injectOrderSyncManager(instance, providesOrderSyncManagerProvider.get());
      return instance;
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.thedasagroup.suminative.data.prefs.Prefs
          return (T) RepoModule_ProvidesSharedPrefsFactory.providesSharedPrefs(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 1: // com.thedasagroup.suminative.HourUtils
          return (T) RepoModule_GetHourUtilsFactory.getHourUtils();

          case 2: // com.instacart.truetime.time.TrueTimeImpl
          return (T) RepoModule_ProvidesTrueTimeFactory.providesTrueTime();

          case 3: // com.thedasagroup.suminative.work.SyncOrdersWorker_AssistedFactory
          return (T) new SyncOrdersWorker_AssistedFactory() {
            @Override
            public SyncOrdersWorker create(Context appContext, WorkerParameters params) {
              return new SyncOrdersWorker(singletonCImpl.providesSyncOrdersUseCaseProvider.get(), appContext, params);
            }
          };

          case 4: // com.thedasagroup.suminative.domain.orders.SyncOrdersUseCase
          return (T) AppUseCaseModule_ProvidesSyncOrdersUseCaseFactory.providesSyncOrdersUseCase(singletonCImpl.providesOrderLocalRepositoryProvider.get(), singletonCImpl.providesStockRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());

          case 5: // com.thedasagroup.suminative.data.database.LocalOrderRepository
          return (T) RepoModule_ProvidesOrderLocalRepositoryFactory.providesOrderLocalRepository(singletonCImpl.providesDatabaseManagerProvider.get());

          case 6: // com.thedasagroup.suminative.data.database.DatabaseManager
          return (T) RepoModule_ProvidesDatabaseManagerFactory.providesDatabaseManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 7: // com.thedasagroup.suminative.data.repo.StockRepository
          return (T) RepoModule_ProvidesStockRepositoryFactory.providesStockRepository();

          case 8: // com.thedasagroup.suminative.work.UploadLogsWorker_AssistedFactory
          return (T) new UploadLogsWorker_AssistedFactory() {
            @Override
            public UploadLogsWorker create(Context appContext2, WorkerParameters params2) {
              return new UploadLogsWorker(singletonCImpl.providesLogsRepositoryProvider.get(), appContext2, params2);
            }
          };

          case 9: // com.thedasagroup.suminative.data.repo.LogsRepository
          return (T) RepoModule_ProvidesLogsRepositoryFactory.providesLogsRepository(singletonCImpl.providesSharedPrefsProvider.get());

          case 10: // com.thedasagroup.suminative.work.OrderSyncManager
          return (T) RepoModule_ProvidesOrderSyncManagerFactory.providesOrderSyncManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 11: // com.thedasagroup.suminative.ui.login.LoginUseCase
          return (T) AppUseCaseModule_ProvidesLoginUseCaseFactory.providesLoginUseCase(singletonCImpl.providesLoginRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());

          case 12: // com.thedasagroup.suminative.data.repo.LoginRepository
          return (T) RepoModule_ProvidesLoginRepositoryFactory.providesLoginRepository();

          case 13: // com.thedasagroup.suminative.ui.orders.GetOrdersUseCase
          return (T) AppUseCaseModule_ProvidesGetOrdersUseCaseFactory.providesGetOrdersUseCase(singletonCImpl.providesOrdersRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());

          case 14: // com.thedasagroup.suminative.data.repo.OrdersRepository
          return (T) RepoModule_ProvidesOrdersRepositoryFactory.providesOrdersRepository();

          case 15: // com.thedasagroup.suminative.ui.orders.GetPendingOrdersPagedUseCase
          return (T) AppUseCaseModule_ProvidePendingOrdersUseCaseFactory.providePendingOrdersUseCase(singletonCImpl.providesOrdersRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());

          case 16: // com.thedasagroup.suminative.ui.orders.GetScheduleOrdersUseCase
          return (T) AppUseCaseModule_ProvidesScheduleOrdersUseCaseFactory.providesScheduleOrdersUseCase(singletonCImpl.providesOrdersRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());

          case 17: // com.thedasagroup.suminative.ui.orders.GetScheduleOrdersPagedUseCase
          return (T) AppUseCaseModule_ProvideScheduleOrdersPagedUseCaseFactory.provideScheduleOrdersPagedUseCase(singletonCImpl.providesOrdersRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());

          case 18: // com.thedasagroup.suminative.ui.orders.ChangeStatusUseCase
          return (T) AppUseCaseModule_ProvideChangeStatusUseCaseFactory.provideChangeStatusUseCase(singletonCImpl.providesSharedPrefsProvider.get(), singletonCImpl.providesOrdersRepositoryProvider.get());

          case 19: // com.thedasagroup.suminative.ui.orders.ChangeStatusAndOrdersUseCase
          return (T) AppUseCaseModule_ProvidesAcceptDeliveryOrderUseCaseFactory.providesAcceptDeliveryOrderUseCase(singletonCImpl.providesSharedPrefsProvider.get(), singletonCImpl.providesOrdersRepositoryProvider.get(), singletonCImpl.providesTrueTimeProvider.get());

          case 20: // com.thedasagroup.suminative.ui.orders.AcceptOrderWithDelayUseCase
          return (T) AppUseCaseModule_ProvidesAcceptOrderWithDelayUseCaseFactory.providesAcceptOrderWithDelayUseCase(singletonCImpl.providesSharedPrefsProvider.get(), singletonCImpl.providesOrdersRepositoryProvider.get(), singletonCImpl.providesTrueTimeProvider.get(), singletonCImpl.provideStoreSettingsUseCaseProvider.get());

          case 21: // com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase
          return (T) AppUseCaseModule_ProvideStoreSettingsUseCaseFactory.provideStoreSettingsUseCase(singletonCImpl.providesLoginRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());

          case 22: // com.thedasagroup.suminative.ui.orders.CloseOpenStoreUseCase
          return (T) AppUseCaseModule_ProvideCloseOpenStoreUseCaseFactory.provideCloseOpenStoreUseCase(singletonCImpl.providesOrdersRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());

          case 23: // com.thedasagroup.suminative.ui.utils.SoundPoolPlayer
          return (T) RepoModule_ProvidesSoundPoolPlayerFactory.providesSoundPoolPlayer(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 24: // android.media.AudioManager
          return (T) RepoModule_ProvidesAudioManagerFactory.providesAudioManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 25: // com.thedasagroup.suminative.domain.GetPOSSettingsUseCase
          return (T) AppUseCaseModule_ProvidesGetPosSettingsUsecaseFactory.providesGetPosSettingsUsecase(singletonCImpl.providesLoginRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());

          case 26: // com.thedasagroup.suminative.ui.stock.StockUseCase
          return (T) OrderUseCaseModule_ProvidesStockUseCaseFactory.providesStockUseCase(singletonCImpl.providesStockRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get(), singletonCImpl.provideProductRepositoryProvider.get(), singletonCImpl.providesCategoriesHelperProvider.get());

          case 27: // com.thedasagroup.suminative.data.repo.ProductRepository
          return (T) AppUseCaseModule_ProvideProductRepositoryFactory.provideProductRepository(singletonCImpl.providesDatabaseManagerProvider.get());

          case 28: // com.thedasagroup.suminative.ui.stock.CategorySortingHelper
          return (T) CategoryModule_ProvidesCategoriesHelperFactory.providesCategoriesHelper(singletonCImpl.providesStockRepositoryProvider.get(), singletonCImpl.provideCategoryRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());

          case 29: // com.thedasagroup.suminative.data.database.CategoryRepository
          return (T) CategoryModule_ProvideCategoryRepositoryFactory.provideCategoryRepository(singletonCImpl.providesDatabaseManagerProvider.get());

          case 30: // com.thedasagroup.suminative.ui.stock.ChangeStockUseCase
          return (T) OrderUseCaseModule_ProvidesUpdateStockUseCaseFactory.providesUpdateStockUseCase(singletonCImpl.providesStockRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get(), singletonCImpl.provideProductRepositoryProvider.get(), singletonCImpl.providesCategoriesHelperProvider.get());

          case 31: // com.thedasagroup.suminative.ui.products.PlaceOnlineOrderUseCase
          return (T) AppUseCaseModule_ProvidesGuavaOrderUseCaseFactory.providesGuavaOrderUseCase(singletonCImpl.providesStockRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get(), singletonCImpl.providesTrueTimeProvider.get(), singletonCImpl.providesMyGuavaRepositoryProvider.get());

          case 32: // com.thedasagroup.suminative.data.repo.MyGuavaRepository
          return (T) RepoModule_ProvidesMyGuavaRepositoryFactory.providesMyGuavaRepository(singletonCImpl.providesTrueTimeProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());

          case 33: // com.thedasagroup.suminative.ui.products.OrderUseCase
          return (T) AppUseCaseModule_ProvidesOrderUseCaseFactory.providesOrderUseCase(singletonCImpl.providesStockRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get(), singletonCImpl.providesTrueTimeProvider.get(), singletonCImpl.providesMyGuavaRepositoryProvider.get(), singletonCImpl.providesOrderLocalRepositoryProvider.get());

          case 34: // com.thedasagroup.suminative.domain.cloud_print.CloudPrintUseCase
          return (T) AppUseCaseModule_ProvidesCloudPrintUseCaseFactory.providesCloudPrintUseCase(singletonCImpl.providesStockRepositoryProvider.get());

          case 35: // com.thedasagroup.suminative.ui.products.OptionDetailsUseCase
          return (T) OrderUseCaseModule_ProvidesOptionDetailsUseCaseFactory.providesOptionDetailsUseCase(singletonCImpl.provideOptionRepositoryProvider.get());

          case 36: // com.thedasagroup.suminative.data.repo.OptionRepository
          return (T) AppUseCaseModule_ProvideOptionRepositoryFactory.provideOptionRepository(singletonCImpl.providesDatabaseManagerProvider.get());

          case 37: // com.thedasagroup.suminative.ui.sales.TotalSalesUseCase
          return (T) AppUseCaseModule_ProvidesSalesUseCaseFactory.providesSalesUseCase(singletonCImpl.providesSalesRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());

          case 38: // com.thedasagroup.suminative.data.repo.SalesRepository
          return (T) RepoModule_ProvidesSalesRepositoryFactory.providesSalesRepository(singletonCImpl.providesTrueTimeProvider.get());

          case 39: // com.thedasagroup.suminative.domain.sales_report.GetSalesReportUseCase
          return (T) AppUseCaseModule_ProvidesSalesReportUsecaseFactory.providesSalesReportUsecase(singletonCImpl.providesSalesRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());

          case 40: // com.thedasagroup.suminative.domain.myguava.MyGuavaGetOrdersUseCase
          return (T) AppUseCaseModule_ProvidesMyGuavaGetOrderUseCaseFactory.providesMyGuavaGetOrderUseCase(singletonCImpl.providesMyGuavaRepositoryProvider.get(), singletonCImpl.providesTrueTimeProvider.get());

          case 41: // com.thedasagroup.suminative.data.repo.WaitersRepository
          return (T) RepoModule_ProvidesWaitersRepositoryFactory.providesWaitersRepository();

          case 42: // com.thedasagroup.suminative.ui.login.StoreUserLoginUseCase
          return (T) AppUseCaseModule_ProvidesStoreUserLoginUseCaseFactory.providesStoreUserLoginUseCase(singletonCImpl.providesClockInOutRepositoryProvider.get());

          case 43: // com.thedasagroup.suminative.data.repo.ClockInOutRepository
          return (T) RepoModule_ProvidesClockInOutRepositoryFactory.providesClockInOutRepository();

          case 44: // com.thedasagroup.suminative.ui.login.ClockInUserTimeUseCase
          return (T) AppUseCaseModule_ProvidesClockInUserTimeUseCaseFactory.providesClockInUserTimeUseCase(singletonCImpl.providesClockInOutRepositoryProvider.get());

          case 45: // com.thedasagroup.suminative.ui.login.ClockOutUserTimeUseCase
          return (T) AppUseCaseModule_ProvidesClockOutUserTimeUseCaseFactory.providesClockOutUserTimeUseCase(singletonCImpl.providesClockInOutRepositoryProvider.get());

          case 46: // com.thedasagroup.suminative.domain.orders.GetLocalOrdersUseCase
          return (T) AppUseCaseModule_ProvidesLocalOrdersUseCaseFactory.providesLocalOrdersUseCase(singletonCImpl.providesOrderLocalRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());

          case 47: // com.thedasagroup.suminative.ui.reservations.GetActiveReservationsUseCase
          return (T) AppUseCaseModule_ProvidesGetActiveReservationsUseCaseFactory.providesGetActiveReservationsUseCase(singletonCImpl.providesReservationsRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get(), singletonCImpl.providesTrueTimeProvider.get());

          case 48: // com.thedasagroup.suminative.data.repo.ReservationsRepository
          return (T) RepoModule_ProvidesReservationsRepositoryFactory.providesReservationsRepository();

          case 49: // com.thedasagroup.suminative.ui.reservations.GetAllReservationsUseCase
          return (T) AppUseCaseModule_ProvidesGetAllReservationsUseCaseFactory.providesGetAllReservationsUseCase(singletonCImpl.providesReservationsRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get(), singletonCImpl.providesTrueTimeProvider.get());

          case 50: // com.thedasagroup.suminative.ui.reservations.CreateReservationUseCase
          return (T) AppUseCaseModule_ProvidesCreateReservationUseCaseFactory.providesCreateReservationUseCase(singletonCImpl.providesReservationsRepositoryProvider.get());

          case 51: // com.thedasagroup.suminative.ui.reservations.EditReservationUseCase
          return (T) AppUseCaseModule_ProvidesEditReservationUseCaseFactory.providesEditReservationUseCase(singletonCImpl.providesReservationsRepositoryProvider.get());

          case 52: // com.thedasagroup.suminative.ui.reservations.CancelReservationUseCase
          return (T) AppUseCaseModule_ProvidesCancelReservationUseCaseFactory.providesCancelReservationUseCase(singletonCImpl.providesReservationsRepositoryProvider.get());

          case 53: // com.thedasagroup.suminative.ui.reservations.GetReservationAreasUseCase
          return (T) AppUseCaseModule_ProvidesGetReservationAreasUseCaseFactory.providesGetReservationAreasUseCase(singletonCImpl.providesReservationsRepositoryProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());

          case 54: // com.thedasagroup.suminative.ui.reservations.GetReservationTablesUseCase
          return (T) AppUseCaseModule_ProvidesGetReservationTablesUseCaseFactory.providesGetReservationTablesUseCase(singletonCImpl.providesReservationsRepositoryProvider.get());

          case 55: // com.thedasagroup.suminative.domain.rewards.GetAllCustomersUseCase
          return (T) AppUseCaseModule_ProvidesGetAllCustomersUseCaseFactory.providesGetAllCustomersUseCase(singletonCImpl.providesRewardsRepositoryProvider.get());

          case 56: // com.thedasagroup.suminative.data.repo.RewardsRepository
          return (T) RepoModule_ProvidesRewardsRepositoryFactory.providesRewardsRepository();

          case 57: // com.thedasagroup.suminative.domain.rewards.AddPointsUseCase
          return (T) AppUseCaseModule_ProvidesAddPointsUseCaseFactory.providesAddPointsUseCase(singletonCImpl.providesRewardsRepositoryProvider.get());

          case 58: // com.thedasagroup.suminative.domain.myguava.MyGuavaCreateOrderUseCase
          return (T) AppUseCaseModule_ProvidesMyGuavaCreateOrderUseCaseFactory.providesMyGuavaCreateOrderUseCase(singletonCImpl.providesMyGuavaRepositoryProvider.get(), singletonCImpl.providesTrueTimeProvider.get(), singletonCImpl.providesSharedPrefsProvider.get());

          case 59: // com.thedasagroup.suminative.domain.myguava.MyGuavaGetTerminalsUseCase
          return (T) AppUseCaseModule_ProvidesMyGuavaGetTerminalsUseCaseFactory.providesMyGuavaGetTerminalsUseCase(singletonCImpl.providesMyGuavaRepositoryProvider.get());

          case 60: // com.thedasagroup.suminative.domain.myguava.MyGuavaCreateSessionUseCase
          return (T) AppUseCaseModule_ProvidesMyGuavaCreateSessionUseCaseFactory.providesMyGuavaCreateSessionUseCase(singletonCImpl.providesMyGuavaRepositoryProvider.get());

          case 61: // com.thedasagroup.suminative.domain.myguava.MyGuavaMakePaymentUseCase
          return (T) AppUseCaseModule_ProvidesMyGuavaMakePaymentUseCaseFactory.providesMyGuavaMakePaymentUseCase(singletonCImpl.providesMyGuavaCreateOrderUseCaseProvider.get(), singletonCImpl.providesMyGuavaGetTerminalsUseCaseProvider.get(), singletonCImpl.providesMyGuavaCreateSessionUseCaseProvider.get());

          case 62: // com.thedasagroup.suminative.domain.myguava.MyGuavaMakeRefundUseCase
          return (T) AppUseCaseModule_ProvidesMyGuavaMakeRefundUseCaseFactory.providesMyGuavaMakeRefundUseCase(singletonCImpl.providesMyGuavaRefundOrderUseCaseProvider.get(), singletonCImpl.providesMyGuavaGetTerminalsUseCaseProvider.get(), singletonCImpl.providesMyGuavaCreateSessionUseCaseProvider.get());

          case 63: // com.thedasagroup.suminative.domain.myguava.MyGuavaCreateRefundOrderUseCase
          return (T) AppUseCaseModule_ProvidesMyGuavaRefundOrderUseCaseFactory.providesMyGuavaRefundOrderUseCase(singletonCImpl.providesMyGuavaRepositoryProvider.get(), singletonCImpl.providesTrueTimeProvider.get());

          case 64: // com.thedasagroup.suminative.domain.myguava.MyGuavaCheckStatusUseCase
          return (T) AppUseCaseModule_ProvidesMyGuavaCheckStatusUseCaseFactory.providesMyGuavaCheckStatusUseCase(singletonCImpl.providesMyGuavaRepositoryProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
